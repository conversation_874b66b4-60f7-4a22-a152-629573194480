#参数解析
#DataSourceName：数据源名或数据库名
#DriverType：驱动类型（QODBC、QPSQL、QOCI、QSQLITE、QMYSQL、SQLITECIPHER）
#DatabaseType：数据库类型（Oracle、DM、postgreSQL、SQLite、MySQL、SQLiteCipher）
#ServerIP：服务器IP地址（本机可写localhost或127.0.0.1）
#Port：端口号（数据库默认值：达梦5236、postgreSQL5432、Oracle1521、MySQL3306）
#User：用户名
#Password：密码

#名字唯一不可修改，MainDB为默认主数据库
[MainDB]
DataSourceName=db_sps
DriverType=QPSQL
DatabaseType=postgreSQL
ServerIP=************
#ServerIP=localhost
Port=5432
User=sps
Password=sps

#名字可修改，TestDB为需要连接的额外数据库
#[TestDB]
#DataSourceName=SCSTS_PC
#DriverType=QODBC
#DatabaseType=DM
#ServerIP=************
#Port=5236
#User=SCSTS_PC
#Password=123456789
