// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#include "LevelInstance/LevelInstanceSubsystem.h"
#include "C_ShpSubsystem.generated.h"

class AC_QuadTreeManage;
/**
 * 
 */
class AC_SplineActor_Ins;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FCurStoreFile,FString,FileName,bool,IsComplete);

UCLASS()
class P_SCENARIOARCHIVE_API UC_ShpSubsystem : public ULevelInstanceSubsystem
{
	GENERATED_BODY()
	
public:
	static UC_ShpSubsystem* Get();
	virtual bool ShouldCreateSubsystem(UObject* Outer) const { return true; };
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;


#pragma region QueryShp
	
public:
	UFUNCTION(BlueprintCallable)
	TArray<FString> QueryFIDByRec(const FString& FilePath,const FVector& RightUpPoint,const FVector& LeftDownPoint);
	
#pragma endregion 

#pragma region ShpSpawn
	
public:
	//将大区域分成链式小区域
	UFUNCTION(BlueprintCallable)
	void DivideCoord(double InminX,double InminY,double InmaxX ,double InmaxY);
	
	UFUNCTION(BlueprintCallable,Category="SpawnShp")
	void SpawnDataByShp(const FString& ShpFileType,const FString ShpDataType,const FString& FilePath);
	//导入对应shp文件后，绑定到Pawn移动输入上查找离Pawn最近的区域
	UFUNCTION(BlueprintCallable)
	void GetLatestArea(float tmp);
private:
	//将所有叶子区域添加进Cesium Camera Manager
	void AddCamera();
	void RemoveCamera();
	
	//导入shp文件后，将区域加载完成后再生成(区域加载完成通知或许要改源码）所有叶子节点区域生成Actor
	void SpawnRoadActor(const FString& filePath);
	
	
	void AttachActors(AActor* InLastActor, AActor* InCurrentActor);
	
	//Actor递归隐藏或显示
	void bSetHidden(AActor* Actor,bool InHidden);

	bool GeoToUECoord(FVector InCoord,FVector& OutCoord);

	bool UEToGeoCoord(FVector InCoord,FVector& OutCoord);


	
#pragma endregion

#pragma region Shp_Upload_Download
public:

	//当前待上传的shp文件列表 文件名和类型
	UPROPERTY(BlueprintReadWrite,Category="Shp_Upload")
	TMap<FString,FString> ShpFileList;

	UPROPERTY(BlueprintAssignable,Category="Shp_Upload")
	FCurStoreFile Event_CurStoreFile;

	//获取文件类型：点、线、面
	UFUNCTION(BlueprintCallable,Category="Shp_Upload")
	FString GetVectorFileType(const FString& FilePath);
	
	//当前导入的shp是否与数据库中当前地图导入的shp文件名重复（同一个地图中文件名不可重复）
	UFUNCTION(BlueprintCallable)
	bool ShpIsRepeat(const int64& mapId,const FString& CurrentShpPath);

	//1、查找或插入map表中的shp_group_id
	int64 UpdateGroupId_map(const int64& mapId);

	//2、
	void Insert_mapShp(const int64& groudId,const FString&ShpPath,const int64& dataId,const FString shpType);

	//3、复制文件到SaveGames目录
	FString CopyShpFile(const int64& mapId,const FString&ShpPath,const TArray<FString>& CopyShpFilePathList);

	//4、
	void StoreData(const FString& DestPath,const int64& dataId);

	//上传shp文件
	UFUNCTION(BlueprintCallable,Category="Shp_Upload")
	void UploadShp(const int64& mapId);
	
	//下载shp文件
	UFUNCTION(BlueprintCallable)
	void DownShpFileByDB(const int64& mapId,TMap<FString,FString>& SourceShpFilePathList);

	//根据data_id获取文件名称
	FString GetFileName(const int64& dataId);

#pragma endregion 

private:
	//区域句柄
	UPROPERTY()
	

	bool bIsSpawnComplete=false;


#pragma region SpawnedActorsManage

private:
	// 已生成的Actor记录，键为区域边界，值为Actor指针
	TMap<FString, AActor*> SpawnedActors;

	
public:
	AActor* LastActor=nullptr;
	AActor* CurrentActor=nullptr;
#pragma endregion SpawnedActors
	
};


