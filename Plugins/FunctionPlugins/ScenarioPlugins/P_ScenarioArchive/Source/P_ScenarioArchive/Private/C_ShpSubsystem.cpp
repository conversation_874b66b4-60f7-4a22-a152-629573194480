// Fill out your copyright notice in the Description page of Project Settings.


#include "C_ShpSubsystem.h"

#include "C_DataAccessBPLibrary.h"
#include "GamePlay/C_PlayerController.h"
#include "HAL/FileManagerGeneric.h"
#include "Kismet/GameplayStatics.h"


UC_ShpSubsystem* UC_ShpSubsystem::Get()
{
	const UObject* WorldContext = GWorld;
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull);
	return World ? World->GetSubsystem< UC_ShpSubsystem>() : nullptr;
}

void UC_ShpSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UC_ShpSubsystem::Deinitialize()
{
	Super::Deinitialize();
}



#pragma region QueryShp

TArray<FString> UC_ShpSubsystem::QueryFIDByRec(const FString& FilePath, const FVector& RightUpPoint,const FVector& LeftDownPoint)
{
	
	return TArray<FString>();
	
}

#pragma endregion QueryShp


#pragma region ShpSpawn
bool UC_ShpSubsystem::GeoToUECoord(FVector InCoord,FVector& OutCoord)
{
	return false;
}

bool UC_ShpSubsystem::UEToGeoCoord(FVector InCoord,FVector& OutCoord)
{
	return false;
}

//将大区域分成链式小区域
void UC_ShpSubsystem::DivideCoord(double InminX, double InminY, double InmaxX, double InmaxY)
{
	
	
}

//将所有叶子区域添加进Cesium Camera Manager
void UC_ShpSubsystem::AddCamera()
{
}

void UC_ShpSubsystem::RemoveCamera()
{
	
}

//所有叶子节点区域生成道路Actor
void UC_ShpSubsystem::SpawnRoadActor(const FString& filePath)
{
	
}




void UC_ShpSubsystem::AttachActors(AActor* InLastActor, AActor* InCurrentActor)
{
	if (InLastActor && InCurrentActor && InLastActor != InCurrentActor)
	{
		FAttachmentTransformRules Rules(EAttachmentRule::KeepWorld, EAttachmentRule::KeepWorld, EAttachmentRule::KeepWorld, false);
		InCurrentActor->AttachToActor(InLastActor, Rules);
	}
}

void UC_ShpSubsystem::GetLatestArea(float tmp)
{
	
}

void UC_ShpSubsystem::SpawnDataByShp(const FString& ShpFileType,const FString ShpDataType,const FString& FilePath)
{
	if(ShpFileType==FString("Line"))
	{
		if(ShpDataType==FString("Road"))
		{
			SpawnRoadActor(FilePath);
		}
	}
	else if(ShpFileType==FString("Polygon"))
	{
		if(ShpDataType==FString("Build"))
		{
			
		}
	}
	else if(ShpFileType==FString("Point"))
	{
		
	}
	RemoveCamera();
	//绑定Pawn移动时显示当前区域Actor
	APlayerController* PlayerController = UGameplayStatics::GetPlayerController(this,0);
	if(PlayerController==nullptr) return;
	AC_PlayerController* PC = Cast<AC_PlayerController>(PlayerController);
	if(PC==nullptr) return;
	PC->EventDispatcher_PRMoveForward.AddDynamic(this,&UC_ShpSubsystem::GetLatestArea);
	PC->EventDispatcher_PRMoveRight.AddDynamic(this,&UC_ShpSubsystem::GetLatestArea);
}

void UC_ShpSubsystem::bSetHidden(AActor* Actor, bool InHidden)
{
	if (Actor == nullptr) return;
	TArray<AActor*> OutActors;
	Actor->GetAttachedActors(OutActors,true,true);
	for (AActor* OutActor : OutActors)
	{
		OutActor->SetActorHiddenInGame(InHidden);
	}
}
#pragma endregion

#pragma region Shp_Upload_Download

FString UC_ShpSubsystem::GetVectorFileType(const FString& FilePath)
{
	return FString("Line");
}

bool UC_ShpSubsystem::ShpIsRepeat(const int64& mapId, const FString& CurrentShpPath)
{
	FString query = FString::Printf(TEXT("SELECT t2.shp_data_path FROM map AS t1 JOIN map_shp AS t2 ON t1.shp_group_id = t2.shp_group_id WHERE t1.id = '%lld'"), mapId);
	int queryID;
	UC_DataAccessBPLibrary::execQuery(query, queryID);
	while (UC_DataAccessBPLibrary::nextRecord(queryID))
	{
		FString ShpPath = UC_DataAccessBPLibrary::getValueAsString(0, queryID);
		
		if (FPaths::GetBaseFilename(CurrentShpPath).Equals(FPaths::GetBaseFilename(ShpPath)))
		{
			UC_DataAccessBPLibrary::closeQuery(queryID);
			return true;
		}
	}
	return false;
}

int64 UC_ShpSubsystem::UpdateGroupId_map(const int64& mapId)
{
	//查找是否有group_id
	int64 ShpGroupId = 0;
	FString query = FString::Printf(TEXT("SELECT shp_group_id FROM map WHERE id = '%lld'"), mapId);
	int queryID;
	UC_DataAccessBPLibrary::execQuery(query, queryID);
	while (UC_DataAccessBPLibrary::nextRecord(queryID))
	{
		ShpGroupId = UC_DataAccessBPLibrary::getValueAsInt64(0, queryID);
	}
	UC_DataAccessBPLibrary::closeQuery(queryID);
	
	if (ShpGroupId == 0)
	{
		//插入group_id
		ShpGroupId = UC_DataAccessBPLibrary::generateSnowflakeId();
		FString sql;
		sql = FString::Printf(TEXT("UPDATE map SET shp_group_id = (?) WHERE id='%lld' "),mapId);
		UC_DataAccessBPLibrary::prepare(sql,queryID);//预备sql语句
		UC_DataAccessBPLibrary::bindValueInt64(0 ,ShpGroupId, queryID);
		UC_DataAccessBPLibrary::execPrepare(queryID);
	}
	return ShpGroupId;
}

void UC_ShpSubsystem::Insert_mapShp(const int64& groudId,const FString&ShpPath,const int64& dataId,const FString shpType)
{
	//更新map_shp中的shp group id和shp_data_path
	int queryID;
	FString sql = FString::Printf(TEXT("INSERT INTO map_shp ( shp_group_id,shp_data_path,shp_data_id,shp_type ) VALUES ( ?,?,?,? )"));
	UC_DataAccessBPLibrary::prepare(sql,queryID);//预备sql语句
	UC_DataAccessBPLibrary::bindValueInt64(0 ,groudId, queryID);
	UC_DataAccessBPLibrary::bindValueString(1 ,ShpPath, queryID);
	UC_DataAccessBPLibrary::bindValueInt64(2 ,dataId, queryID);
	UC_DataAccessBPLibrary::bindValueString(3 ,shpType, queryID);
	UC_DataAccessBPLibrary::execPrepare(queryID);
}

FString UC_ShpSubsystem::CopyShpFile(const int64& mapId,const FString&ShpPath,const TArray<FString>& SourceShpFilePathList)
{
	//复制shp文件到新的目录
	FString DestPath = FPaths::ProjectSavedDir()+TEXT("SaveGames/")+UC_DataAccessBPLibrary::convertInt64ToFString(mapId)+TEXT("/")+FPaths::GetBaseFilename(ShpPath);
	
	if (IFileManager::Get().DirectoryExists(*DestPath))  IFileManager::Get().DeleteDirectory(*DestPath);
	IFileManager::Get().MakeDirectory(*DestPath, true);

	//将文件复制到SaveGames目录
	for (FString SourceShpFilePath : SourceShpFilePathList)
	{
		FString DestCopyPath = DestPath+TEXT("/")+FPaths::GetCleanFilename(SourceShpFilePath);
		DestCopyPath = FPaths::ConvertRelativePathToFull(DestCopyPath);
		SourceShpFilePath = FPaths::ConvertRelativePathToFull(SourceShpFilePath);
		IFileManager::Get().Copy(*DestCopyPath, *SourceShpFilePath);
	}
	return DestPath;
}

void UC_ShpSubsystem::StoreData(const FString& DestPath,const int64& dataId)
{
	//存储数据
	int queryID;
	TArray<uint8> fileData = UC_DataAccessBPLibrary::CompressDirToCharData(DestPath);
	FString sql = FString::Printf(TEXT("INSERT INTO map_exter_data ( data_id,map_data ) VALUES ( ?,? )"));
	UC_DataAccessBPLibrary::prepare(sql,queryID);//预备sql语句
	UC_DataAccessBPLibrary::bindValueInt64(0 ,dataId, queryID);
	UC_DataAccessBPLibrary::bindValueByteArray(1,fileData, queryID);
	UC_DataAccessBPLibrary::execPrepare(queryID);
}

void UC_ShpSubsystem::UploadShp(const int64& mapId)
{
	if (ShpFileList.IsEmpty()) return;

	//1、查找或插入map表中的shp_group_id
	int64 ShpGroupId = UpdateGroupId_map(mapId);
	
	//提取shp文件目录中所有的同名文件
	TArray<FString> FoundShpFileList;
	TArray<FString> CopyShpFilePathList;
	for (TPair<FString,FString> ShpPath : ShpFileList)
	{
		FFileManagerGeneric::Get().FindFiles(FoundShpFileList,*FPaths::GetPath(ShpPath.Key));
		if(FoundShpFileList.IsEmpty()) continue;
		
		//查找同名文件
		CopyShpFilePathList.Empty();
		for (FString FilePath : FoundShpFileList)
		{
			FString FileName = FPaths::GetBaseFilename(FilePath);
			if (FileName == FPaths::GetBaseFilename(ShpPath.Key))
				CopyShpFilePathList.AddUnique(FPaths::GetPath(ShpPath.Key)+"/"+FilePath);
		}
		if (CopyShpFilePathList.IsEmpty()) continue;
		
		//广播正在存储的文件名
		Event_CurStoreFile.Broadcast(FPaths::GetBaseFilename(ShpPath.Key),false);
		
		//2、插入map_shp数据
		int64 DataId = UC_DataAccessBPLibrary::generateSnowflakeId();
		Insert_mapShp(ShpGroupId,ShpPath.Key,DataId,ShpPath.Value);

		//3、复制文件到SaveGames目录
		FString DestPath = CopyShpFile(mapId,ShpPath.Key,CopyShpFilePathList);

		//4、将目录内文件存入数据库
		StoreData(DestPath,DataId);
	}
	ShpFileList.Empty();
	Event_CurStoreFile.Broadcast(TEXT(""),true);
}

void UC_ShpSubsystem::DownShpFileByDB(const int64& mapId,TMap<FString,FString>& SourceShpFilePathList)
{
	//56 64 25
	//直接把数据库中的shp文件下载到本地，不管之前的源路径
	FString combinedQuery = FString::Printf(
		TEXT("SELECT t3.map_data,t3.data_id,t2.shp_type FROM map AS t1 ")
		TEXT("JOIN map_shp AS t2 ON t1.shp_group_id = t2.shp_group_id ")
		TEXT("JOIN map_exter_data AS t3 ON t2.shp_data_id = t3.data_id ")
		TEXT("WHERE t1.id = '%lld'"), mapId);

	int queryID;
	UC_DataAccessBPLibrary::execQuery(combinedQuery, queryID);
	while (UC_DataAccessBPLibrary::nextRecord(queryID))
	{
		int64 data_id = UC_DataAccessBPLibrary::getValueAsInt64(1, queryID);
		FString CurFileName = GetFileName(data_id);
		
		FString path=FPaths::ProjectSavedDir()+"SaveGames/"+UC_DataAccessBPLibrary::convertInt64ToFString(mapId)+"/"+CurFileName;
		path=FPaths::ConvertRelativePathToFull(path);
		UC_DataAccessBPLibrary::DecompressCharDataToDir(path,UC_DataAccessBPLibrary::getValueAsByteArray(0, queryID));

		FString shpType = UC_DataAccessBPLibrary::getValueAsString(2, queryID);
		SourceShpFilePathList.Emplace(path+"/"+CurFileName+".shp",shpType);
	}
}

FString UC_ShpSubsystem::GetFileName(const int64& dataId)
{
	FString ShpPath;
	FString query = FString::Printf(TEXT("SELECT shp_data_path FROM map_shp WHERE shp_data_id = '%lld' "), dataId);
	int queryID;
	UC_DataAccessBPLibrary::execQuery(query, queryID);
	while (UC_DataAccessBPLibrary::nextRecord(queryID))
	{
		ShpPath = UC_DataAccessBPLibrary::getValueAsString(0, queryID);
	}
	ShpPath=FPaths::GetBaseFilename(ShpPath);
	return ShpPath;
}


#pragma endregion



