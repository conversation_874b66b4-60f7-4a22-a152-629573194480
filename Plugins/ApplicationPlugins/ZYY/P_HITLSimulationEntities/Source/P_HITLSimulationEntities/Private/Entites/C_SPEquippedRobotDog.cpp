// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利


//#include "Component/EntityComponent/Weapon/C_BaseWeaponComponent.h"


#include "Public/Entites/C_SPEquippedRobotDog.h"
// Sets default values
AC_SPEquippedRobotDog::AC_SPEquippedRobotDog(const FObjectInitializer& ObjectInitializer)
	:Super(ObjectInitializer)
{
	// Set this character to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	//PrimaryActorTick.bCanEverTick = true;
	
}



// Called when the game starts or when spawned
void AC_SPEquippedRobotDog::BeginPlay()
{
	Super::BeginPlay();
	//OnAimCompleteDelegate.BindDynamic(this, &AC_SPEquippedRobotDog::NofifyTurnComplete);
}

// Called every frame
void AC_SPEquippedRobotDog::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

// Called to bind functionality to input
void AC_SPEquippedRobotDog::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);
}

/*
bool AC_SPEquippedRobotDog::AimToRotation_Implementation(UC_BaseWeaponComponent* Weapon, FRotator Rotation, const FSPAimCompleteDelegate& OnAimFinished)
{
	if (Weapon == nullptr)
	{
		OnAimFinished.Execute(true);
		return false;
	}

	return Weapon->AimToRotation(Rotation, OnAimFinished);
}
*/


void AC_SPEquippedRobotDog::WeaponTurn_Implementation(double yaw, double pitch, const FActionCompleteDelegate& OnTurnComplete)
{
	/*UC_BaseWeaponComponent* Weapon = FindComponentByClass<UC_BaseWeaponComponent>();//GetEquippedWeapon();
	if (!Weapon)
	{
		UE_LOG(LogTemp, Warning, TEXT("========No WeaponComponent finded!======"));
		return;
	}
	OnTurnCompleteDelegate = OnTurnComplete;
	FRotator Rot(yaw, pitch, 0.f);
	AimToRotation_Implementation(Weapon, Rot, OnAimCompleteDelegate);//要绑个事件反馈旋转完成*/

	
	OnEquipTurnCompleteDelegate = OnTurnComplete;

}

void AC_SPEquippedRobotDog::WeaponStrike_Implementation(bool strike, const FActionCompleteDelegate& OnStrikeComplete)
{
	/*UC_BaseWeaponComponent* Weapon = FindComponentByClass<UC_BaseWeaponComponent>();//GetEquippedWeapon();
	if (!Weapon)
	{
		UE_LOG(LogTemp, Warning, TEXT("========No WeaponComponent finded!======"));
		return;
	}
	FFireCompleteDelegate OnFireFinished;
	Fire_Implementation(OnFireFinished, 1, false, Weapon);*/

}

void AC_SPEquippedRobotDog::NofifyTurnComplete(bool isValid)
{
	OnEquipTurnCompleteDelegate.ExecuteIfBound();
	OnEquipTurnCompleteDelegate.Clear();
}

