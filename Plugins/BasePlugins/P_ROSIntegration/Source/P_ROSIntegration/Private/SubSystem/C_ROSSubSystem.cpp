// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利


#include "SubSystem/C_ROSSubSystem.h"

#include "C_EnumHelperLibrary.h"
#include "C_LogCategory.h"
#include "Kismet/GameplayStatics.h"
#include "ROSBridgeMsg/C_BaseROSBridgeMsg.h"
#include "Transport/C_ROSBridgeConnection.h"
#include "Transport/C_Topic.h"
#include "ROSMsg/C_ROSTime.h"
#include "ROSMsg/ROSGraphMsgs/C_ClockMsg.h"
#include "Settings/C_ROSSetting.h"


TMap<FString, UClass*> UC_ROSSubSystem::ROSMsgClasses;
TMap<FString, UClass*> UC_ROSSubSystem::ROSBridgeClasses;

bool UC_ROSSubSystem::ShouldCreateSubsystem(UObject* Outer) const
{
	InitSupportROSMsg();
	UC_ROSSetting* RosSetting = UC_ROSSetting::Get();
	if (RosSetting == nullptr || !RosSetting->bEnable)
		return false;

	if (RosSetting->ROSEndPoints.IsEmpty())
		return false;

	return true;
}

void UC_ROSSubSystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	
	UE_LOG(LogROSIntegration, Display, TEXT("ROS SubSystem - Init"));
	const UC_ROSSetting* RosSetting = UC_ROSSetting::Get();
	if (RosSetting == nullptr)
		return;

	ROSConnections.Reset();
	ROSConnections.Init(nullptr, RosSetting->ROSEndPoints.Num());

	const int NumOfEndPoint = RosSetting->ROSEndPoints.Num();
	for (int32 Index = 0; Index < NumOfEndPoint; Index++)
		EstablishROSConnection(Index);

	PostAllConnectionEstablished();
	
}

void UC_ROSSubSystem::Deinitialize()
{
	const UC_ROSSetting* RosSetting = UC_ROSSetting::Get();

	for(const auto Connection: ROSConnections)
	{
		Connection->ShutDown();
	}
	ROSConnections.Reset();
	
	if (RosSetting == nullptr)
		return;
	
	if (RosSetting->bSimulateTime)
	{
		FWorldDelegates::OnWorldTickStart.RemoveAll(this);
	}
	
	UE_LOG(LogROSIntegration, Display, TEXT("ROS SubSystem - shutdown done"));
	Super::Deinitialize();
	
}

UC_ROSBridgeConnection* UC_ROSSubSystem::GetROSBridgeConnection(int32 ID)
{
	if (ID == INDEX_NONE)
	{
		for (auto ROSCore: ROSConnections)
		{
			if (ROSCore->IsAlive())
				return ROSCore;
		}

		if (!ROSConnections.IsEmpty())
			return ROSConnections[0];
	}
	else
	{
		const UC_ROSSetting* RosSetting = UC_ROSSetting::Get();
		if (RosSetting == nullptr)
			return nullptr;

		if (!RosSetting->ROSEndPoints.IsValidIndex(ID))
			return nullptr;
		
		return ROSConnections[ID];
	}
	return nullptr;
}

UC_ROSSubSystem* UC_ROSSubSystem::Get()
{
	UWorld* World = GWorld;
	UGameInstance* GameInstance = World ? UGameplayStatics::GetGameInstance(World) : nullptr;
	if (GameInstance != nullptr)
    {
        return GameInstance ? GameInstance->GetSubsystem<UC_ROSSubSystem>() : nullptr;
    }

    const TIndirectArray<FWorldContext>& WorldContexts = GEngine->GetWorldContexts();
    for (const FWorldContext& WorldContext : WorldContexts)
    {
        if (WorldContext.World() && (WorldContext.WorldType == EWorldType::PIE || WorldContext.WorldType == EWorldType::Game))
        {
            GameInstance = UGameplayStatics::GetGameInstance(WorldContext.World());
            break;
        }
    }
	return GameInstance ? GameInstance->GetSubsystem<UC_ROSSubSystem>() : nullptr;
}

bool UC_ROSSubSystem::IsSupportROSMsgType(const FString& InMsgType)
{
	return ROSMsgClasses.Contains(InMsgType);
}

FString UC_ROSSubSystem::GetROSBridgeProtocolStr(const EROSBridgeProtocol& InProtocol)
{
	FText ProtocolStr;
	bool bSuccess = UC_EnumHelperLibrary::GetEnumDisplayName(InProtocol, ProtocolStr);
	return bSuccess ? ProtocolStr.ToString() : FString();
}

UClass* UC_ROSSubSystem::GetROSMsgClass(const FString& InMsgType)
{
	if (!ROSMsgClasses.Contains(InMsgType))
		return nullptr;

	return ROSMsgClasses[InMsgType];
}

UClass* UC_ROSSubSystem::GetROSBridgeClass(const FString& InProtocol)
{
	if (!ROSBridgeClasses.Contains(InProtocol))
		return nullptr;

	return ROSBridgeClasses[InProtocol];
}

void UC_ROSSubSystem::EstablishROSConnection(const int32 ID)
{
	const UC_ROSSetting* RosSetting = UC_ROSSetting::Get();
	if (RosSetting == nullptr)
		return;

	if (!RosSetting->ROSEndPoints.IsValidIndex(ID))
		return;

	// 初始化连接
	ROSConnections[ID] = NewObject<UC_ROSBridgeConnection>(GetGameInstance());
	ROSConnections[ID]->Init(RosSetting->ROSEndPoints[ID]);
}

void UC_ROSSubSystem::PostAllConnectionEstablished()
{
	const UC_ROSSetting* RosSetting = UC_ROSSetting::Get();
	if (RosSetting == nullptr)
		return;

	// 首先关闭模拟时间（调用Now时才能获取到当前系统时间）
	FROSTime::SetUseSimTime(false);
	
	// 启用模拟时间
	if (RosSetting->bSimulateTime)
	{
		FApp::SetFixedDeltaTime(RosSetting->FixedUpdateInterval);
		FApp::SetUseFixedTimeStep(RosSetting->bUseFixedUpdateInterval);

		// 先获取一个当前的系统时间
		const FROSTime Now = FROSTime::Now();
		
		// 告诉ROSIntegration采用模拟时间
		FROSTime::SetUseSimTime(true);
		// 使模拟时间和当前的系统时间保持一致
		FROSTime::UpdateSimTime(Now);
		
		FWorldDelegates::OnWorldTickStart.AddUObject(this, &ThisClass::OnWorldTickStart);
		ClockTopic = NewObject<UC_Topic>(this);
		ClockTopic->Init(RosSetting->ClockTopicName, FString(TEXT("rosgraph_msgs/Clock")), 3);
		ClockTopic->Advertise();
	}
}

void UC_ROSSubSystem::OnWorldTickStart(UWorld* World, ELevelTick TickType, float DeltaTime) const
{
	const UC_ROSSetting* RosSetting = UC_ROSSetting::Get();
	if (RosSetting == nullptr)
		return;

	// 更新模拟时间并发布到ROS系统
	if (RosSetting->bSimulateTime && TickType == LEVELTICK_TimeOnly)
	{
		FApp::SetFixedDeltaTime(RosSetting->FixedUpdateInterval);
		FApp::SetUseFixedTimeStep(RosSetting->bUseFixedUpdateInterval);

		FROSTime Now = FROSTime::Now();

		Now += DeltaTime;

		// 更新模拟时间
		FROSTime::UpdateSimTime(Now);

		// 发布模拟时间
		UC_ClockMsg* ClockMessage = NewObject<UC_ClockMsg>(ClockTopic);
		ClockTopic->Publish(ClockMessage);
	}
}

void UC_ROSSubSystem::InitSupportROSMsg()
{
	// 遍历所有的类
	for (TObjectIterator<UClass> It; It; ++It)
	{
		UClass* Class = *It;

		// 确保该类是 UC_BaseROSMsg 的子类，并且不是抽象类
		if (Class->IsChildOf(UC_BaseROSMsg::StaticClass()) && !Class->HasAnyClassFlags(CLASS_Abstract))
		{
			UC_BaseROSMsg* CDO = Cast<UC_BaseROSMsg>(Class->GetDefaultObject());
			if (CDO && !CDO->GetMsgType().IsEmpty())
			{
				ROSMsgClasses.Add(CDO->GetMsgType(), Class);
			}
		}
		
		// 确保该类是 UC_BaseROSBridgeMsg 的子类，并且不是抽象类
		if (Class->IsChildOf(UC_BaseROSBridgeMsg::StaticClass()) && !Class->HasAnyClassFlags(CLASS_Abstract))
		{
			UC_BaseROSBridgeMsg* CDO = Cast<UC_BaseROSBridgeMsg>(Class->GetDefaultObject());
			if (CDO && CDO->GetBridgeProtocol() != EROSBridgeProtocol::UnDefined)
			{
				ROSBridgeClasses.Add(GetROSBridgeProtocolStr(CDO->GetBridgeProtocol()), Class);
			}
		}
	}
}
