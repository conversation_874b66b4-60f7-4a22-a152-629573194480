// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "H5Ipublic.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "C_HDF5Util.generated.h"

/**
 * 
 */
UCLASS()
class P_HDF5_API UC_HDF5Util : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/**
	 * 初始化HDF5文件
	 * @return 
	 */
	UFUNCTION(BlueprintCallable, Category="HDF5Util")
	static bool InitHDF5();

	/**
	 * 释放当前HDF5文件
	 */
	UFUNCTION(BlueprintCallable, Category="HDF5Util")
	static void ReleaseHDF5();

	/**
	 * 写入图像数据到当前打开的HDF5文件中
	 * @param ImageGroup 图像组名
	 * @param ImageData 图像数据
	 * @param Width 图像宽度
	 * @param Height 图像高度
	 * @param Channels 图像通道
	 * @return 
	 */
	UFUNCTION(BlueprintCallable, Category="HDF5Util")
	static void WriteImageToHDF5(const FString& ImageGroup, const TArray<uint8>& ImageData, const int32 Width, const int32 Height, const int32 Channels);

	/**
	 * 写入关节数据到当前打开的HDF5文件中
	 * @param JointGroup 关节组名
	 * @param JointData 关节偏移数据
	 * @param JointVelocities 关节速度
	 * @param JointNames 关节名称
	 * @return 
	 */
	UFUNCTION(BlueprintCallable, Category="HDF5Util")
	static void WriteJointDataToHDF5(const FString& JointGroup, const TArray<double>& JointData, const TArray<double>& JointVelocities, const TArray<FString>& JointNames);

	/**
	 * 写入里程计数器数据到当前打开的HDF5文件中
	 * @param OdometryGroup 里程计数器组名
	 * @param Position 位置
	 * @param Rotation 旋转 
	 * @param LinearVelocity 线速度 
	 * @param AngularVelocity 角速度
	 */
	UFUNCTION(BlueprintCallable, Category="HDF5Util")
	static void WriteOdometryToHDF5(const FString& OdometryGroup, const FVector& Position, const FQuat& Rotation, const FVector& LinearVelocity, const FVector& AngularVelocity);

	/**
	 * 写入IMU数据到当前打开的HDF5文件中
	 * @param IMUGroup 
	 * @param Orientation 
	 * @param AngularVelocity 
	 * @param LinearAcceleration 
	 */
	UFUNCTION(BlueprintCallable, Category="HDF5Util")
	static void WriteIMUDataToHDF5(const FString& IMUGroup, const FQuat& Orientation, const FVector& AngularVelocity, const FVector& LinearAcceleration);
private:
	static hid_t H5FileHandle;
	static FCriticalSection WriteFileCriticalSection;
	static hid_t NativeUCharType;
	static hid_t NativeFloatType;
	static hid_t NativeIntType;
	static hid_t NativeDoubleType;
	static hid_t NativeStringType;
};
