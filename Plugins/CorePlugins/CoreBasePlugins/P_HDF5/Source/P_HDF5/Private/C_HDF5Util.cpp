// Fill out your copyright notice in the Description page of Project Settings.


#include "C_HDF5Util.h"

#include "C_HDF5LogCategory.h"
#include "hdf5.h"

hid_t UC_HDF5Util::H5FileHandle = -1;
FCriticalSection UC_HDF5Util::WriteFileCriticalSection;
hid_t UC_HDF5Util::NativeUCharType = -1;
hid_t UC_HDF5Util::NativeFloatType = -1;
hid_t UC_HDF5Util::NativeIntType = -1;
hid_t UC_HDF5Util::NativeDoubleType = -1;
hid_t UC_HDF5Util::NativeStringType = -1;

herr_t ErrorCallback(unsigned Index, const H5E_error2_t *ErrorDesc, void *ClientData)
{
	if (ErrorDesc == nullptr)
		return -1;
	
	char Maj[256] = {0}, Min[256] = {0};
	UE_LOG(LogHDF5, Error, TEXT("  Description: %hs"), ANSI_TO_TCHAR(ErrorDesc->desc));
	H5Eget_msg(ErrorDesc->maj_num, nullptr, Maj, sizeof(Maj));
	H5Eget_msg(ErrorDesc->min_num, nullptr, Min, sizeof(Min));
	UE_LOG(LogHDF5, Error, TEXT("  Major: %hs"), ANSI_TO_TCHAR(Maj));
	UE_LOG(LogHDF5, Error, TEXT("  Minor: %hs"), ANSI_TO_TCHAR(Min));
	return 0;
}

void PrintErrorStack()
{
	H5Ewalk2(H5E_DEFAULT, H5E_WALK_DOWNWARD, ErrorCallback, nullptr);
}

FString GetRegularGroupName(FString OldGroupName)
{
	FString NewGroupName = OldGroupName;
	if (NewGroupName.StartsWith("/"))
	{
		NewGroupName.RemoveAt(0, 1);
	}
	NewGroupName.ReplaceCharInline('/', '_');
	return NewGroupName;
}

bool UC_HDF5Util::InitHDF5()
{
	FScopeLock Lock(&WriteFileCriticalSection); // 与写入相同的锁
	// 获取当前时间字符串
	const FString CurrentTimeStr = FDateTime::Now().ToString();
	FString SanitizedFileName = CurrentTimeStr;
	// 清理文件名中的非法字符
	SanitizedFileName.ReplaceCharInline(':', '_');
	SanitizedFileName.ReplaceCharInline(' ', '_');
	SanitizedFileName.ReplaceCharInline('-', '_');
	SanitizedFileName.ReplaceCharInline('.', '_');
	
	FString FileName = FString::Printf(TEXT("%s.h5"), *SanitizedFileName);
	const FString FilePath = FPaths::Combine(FPaths::ProjectSavedDir(), FileName);

	// 创建HDF5文件
	H5FileHandle = H5Fcreate(TCHAR_TO_ANSI(*FilePath), H5F_ACC_TRUNC, H5P_DEFAULT, H5P_DEFAULT);
	
	UE_LOG(LogHDF5, Display, TEXT("HDF5 file creation: Path=%s, Handle=%lld"), *FilePath, static_cast<long long>(H5FileHandle));
	
	if (H5FileHandle <= 0)
	{
		UE_LOG(LogHDF5, Error, TEXT("Failed to create HDF5 file: %s"), *FilePath);
		PrintErrorStack();
		return false; 
	}

	NativeUCharType = H5Tcreate(H5T_INTEGER, sizeof(unsigned char));
	if (NativeUCharType < 0)
	{
		UE_LOG(LogHDF5, Error, TEXT("Failed to create unsigned char type"));
		PrintErrorStack();
		return false;
	}
	H5Tset_order(NativeUCharType, H5T_ORDER_LE);
	H5Tset_sign(NativeUCharType, H5T_SGN_NONE);

	NativeFloatType = H5Tcreate(H5T_FLOAT, sizeof(float));
	if (NativeFloatType < 0)
	{
		UE_LOG(LogHDF5, Error, TEXT("Failed to create float type"));
		PrintErrorStack();
		return false;
	}
	H5Tset_order(NativeFloatType, H5T_ORDER_LE);
	
	NativeIntType = H5Tcreate(H5T_INTEGER, sizeof(int));
	if (NativeIntType < 0)
	{
		UE_LOG(LogHDF5, Error, TEXT("Failed to create int type"));
		PrintErrorStack();
		return false;
	}
	H5Tset_order(NativeIntType, H5T_ORDER_LE);
	H5Tset_sign(NativeIntType, H5T_SGN_2);
	
	NativeDoubleType = H5Tcreate(H5T_FLOAT, sizeof(double));
	if (NativeDoubleType < 0)
	{
		UE_LOG(LogHDF5, Error, TEXT("Failed to create double type"));
		PrintErrorStack();
		return false;
	}
	H5Tset_order(NativeDoubleType, H5T_ORDER_LE);

	NativeStringType = H5Tcreate(H5T_STRING, H5T_VARIABLE);
	if (NativeStringType < 0)
	{
		UE_LOG(LogHDF5, Error, TEXT("Failed to create string type"));
		PrintErrorStack();
		return false;
	}

	return true;  // 返回true表示成功
}

void UC_HDF5Util::ReleaseHDF5()
{
	FScopeLock Lock(&WriteFileCriticalSection); // 与写入相同的锁
	if (H5FileHandle > 0)
	{
		H5Fclose(H5FileHandle);
		H5FileHandle = -1;
	}

	if (NativeUCharType > 0)
	{
		H5Tclose(NativeUCharType);
		NativeUCharType = -1;
	}

	if (NativeFloatType > 0)
	{
		H5Tclose(NativeFloatType);
		NativeFloatType = -1;
	}
	
	if (NativeIntType > 0)
	{
		H5Tclose(NativeIntType);
		NativeIntType = -1;
	}
	
	if (NativeDoubleType > 0)
	{
		H5Tclose(NativeDoubleType);
		NativeDoubleType = -1;
	}
	
	if (NativeStringType > 0)
	{
		H5Tclose(NativeStringType);
		NativeStringType = -1;
	}
}

void UC_HDF5Util::WriteImageToHDF5(const FString& ImageGroup, const TArray<uint8>& ImageData, const int32 Width, const int32 Height, const int32 Channels)
{
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [ImageGroup, ImageData, Width, Height, Channels]()
	{
		FString RegularGroupName = GetRegularGroupName(ImageGroup);
		FScopeLock ScopeLock(&WriteFileCriticalSection);
		if (H5FileHandle <= 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("H5FileHandle is invalid: %lld"), static_cast<long long>(H5FileHandle));
			return;
		}

		// 验证输入参数
		if (Width <= 0 || Height <= 0 || Channels <= 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Invalid image dimensions: Width=%d, Height=%d, Channels=%d"), Width, Height, Channels);
			return;
		}
		
		if (ImageData.Num() != Width * Height * Channels)
		{
			UE_LOG(LogHDF5, Error, TEXT("Image data size mismatch: Expected=%d, Actual=%d"), Width * Height * Channels, ImageData.Num());
			return;
		}
		
		// 首先判断图像组是否存在，不存在则创建对应的组
		hid_t GroupID;
		if (H5Lexists(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT) <= 0)
		{
			GroupID = H5Gcreate2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
			if (GroupID <= 0)
			{
				UE_LOG(LogHDF5, Error, TEXT("Failed to create image group: %s"), *RegularGroupName)
				PrintErrorStack();
				return;
			}
		}
		else
		{
			GroupID = H5Gopen2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT);
			if (GroupID <= 0)
			{
				UE_LOG(LogHDF5, Error, TEXT("Failed to open image group: %s"), *RegularGroupName)
				PrintErrorStack();
				return;
			}
		}
		
		// 定义数据集维度
		const hsize_t Dims[3] = {static_cast<hsize_t>(Height), static_cast<hsize_t>(Width), static_cast<hsize_t>(Channels)};
		const hid_t DataspaceID = H5Screate_simple(3, Dims, nullptr);
		
		// 检查DataspaceID是否创建成功
		if (DataspaceID <= 0)
		{
			PrintErrorStack();
			H5Gclose(GroupID);
			return;
		}
		
		// 创建数据集,使用当前时间作为数据集名称
		static int32 Counter = 0;
		FDateTime Now = FDateTime::Now();
		const FString FinalDatasetName = FString::Printf(TEXT("%d_%d_%d_%d"), Now.GetHour(), Now.GetMinute(), Now.GetSecond(), ++Counter);
		const std::string DatasetNameUTF8 = TCHAR_TO_UTF8(*FinalDatasetName);
		if (H5Lexists(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT) > 0)
		{
		    H5Ldelete(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT);
        }
		
		const hid_t DatasetID = H5Dcreate2(
			GroupID,
			DatasetNameUTF8.c_str(),
			NativeUCharType,
			DataspaceID, 
			H5P_DEFAULT,
			H5P_DEFAULT,
			H5P_DEFAULT
			);

		// 检查DatasetID是否有效
		if (DatasetID <= 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to create dataset: %s"), *FinalDatasetName);
			PrintErrorStack();
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}
		
		// 写入数据
		herr_t Status = H5Dwrite(DatasetID, NativeUCharType, H5S_ALL, H5S_ALL, H5P_DEFAULT, ImageData.GetData());
		if (Status < 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to write dataset: %s"), *FinalDatasetName);
			PrintErrorStack();
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
		}
		
		// 增加元数据
		const hid_t AttrSpace = H5Screate(H5S_SCALAR);
		if (AttrSpace <= 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to create attribute space"));
			PrintErrorStack();
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}
		const hid_t WidthAttr = H5Acreate2(DatasetID, "Width", NativeIntType, AttrSpace, H5P_DEFAULT, H5P_DEFAULT);
		if (WidthAttr <= 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to create Width attribute"));
			PrintErrorStack();
			H5Sclose(AttrSpace);
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}
		
		Status = H5Awrite(WidthAttr, NativeIntType, &Width);
		if (Status < 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to write Width attribute"));
			PrintErrorStack();
			H5Aclose(WidthAttr);
			H5Sclose(AttrSpace);
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}

		const hid_t HeightAttr = H5Acreate2(DatasetID, "Height", NativeIntType, AttrSpace, H5P_DEFAULT, H5P_DEFAULT);
		if (HeightAttr <= 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to create Height attribute"));
			PrintErrorStack();
			H5Aclose(WidthAttr);
			H5Sclose(AttrSpace);
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}
		
		Status = H5Awrite(HeightAttr, NativeIntType, &Height);
		if (Status < 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to write Height attribute"));
			PrintErrorStack();
			H5Aclose(HeightAttr);
			H5Aclose(WidthAttr);
			H5Sclose(AttrSpace);
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}

		const hid_t ChannelsAttr = H5Acreate2(DatasetID, "Channels", NativeIntType, AttrSpace, H5P_DEFAULT, H5P_DEFAULT);
		if (ChannelsAttr <= 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to create Channels attribute"));
			PrintErrorStack();
			H5Aclose(HeightAttr);
			H5Aclose(WidthAttr);
			H5Sclose(AttrSpace);
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}
		
		Status = H5Awrite(ChannelsAttr, NativeIntType, &Channels);
		if (Status < 0)
		{
			UE_LOG(LogHDF5, Error, TEXT("Failed to write channel attribute"));
			PrintErrorStack();
			H5Aclose(HeightAttr);
			H5Aclose(WidthAttr);
			H5Sclose(AttrSpace);
			H5Dclose(DatasetID);
			H5Sclose(DataspaceID);
			H5Gclose(GroupID);
			return;
		}
		
		H5Aclose(WidthAttr);
		H5Aclose(HeightAttr);
		H5Aclose(ChannelsAttr);
		H5Sclose(AttrSpace);
		H5Dclose(DatasetID);
		H5Sclose(DataspaceID);
		H5Gclose(GroupID);
	});
}

void UC_HDF5Util::WriteJointDataToHDF5(const FString& JointGroup, const TArray<double>& JointData, const TArray<double>& JointVelocities, const TArray<FString>& JointNames)
{
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [JointGroup, JointData, JointVelocities, JointNames]()
    {
    	FString RegularGroupName = GetRegularGroupName(JointGroup);
        FScopeLock ScopeLock(&WriteFileCriticalSection);
        if (H5FileHandle <= 0)
        {
        	UE_LOG(LogHDF5, Error, TEXT("H5FileHandle is invalid: %lld"), static_cast<long long>(H5FileHandle));
            return;
        }

        // 验证数据有效性
        const int32 JointCount = JointNames.Num();
        if (JointCount == 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("JointNames array is empty"));
            return;
        }
        
        if (JointData.Num() != JointCount)
        {
            UE_LOG(LogHDF5, Error, TEXT("Joint data size mismatch: Expected=%d, Actual=%d"), JointCount, JointData.Num());
            return;
        }
        
        if (JointVelocities.Num() != JointCount)
        {
            UE_LOG(LogHDF5, Error, TEXT("Joint velocities size mismatch: Expected=%d, Actual=%d"), JointCount, JointVelocities.Num());
            return;
        }

        // 首先判断关节组是否存在，不存在则创建对应的组
        hid_t GroupID;
        if (H5Lexists(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT) <= 0)
        {
            GroupID = H5Gcreate2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
            if (GroupID <= 0)
            {
                UE_LOG(LogHDF5, Error, TEXT("Failed to create joint group: %s"), *RegularGroupName);
            	PrintErrorStack();
                return;
            }
        }
        else
        {
            GroupID = H5Gopen2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT);
            if (GroupID <= 0)
            {
                UE_LOG(LogHDF5, Error, TEXT("Failed to open joint group: %s"), *RegularGroupName);
                PrintErrorStack();
                return;
            }
        }
        
        // 创建当前时间作为数据集名称
        static int32 Counter = 0;
        FDateTime Now = FDateTime::Now();
        const FString FinalDatasetName = FString::Printf(TEXT("%d_%d_%d_%d"), Now.GetHour(), Now.GetMinute(), Now.GetSecond(), ++Counter);
        const std::string DatasetNameUTF8 = TCHAR_TO_UTF8(*FinalDatasetName);
        if (H5Lexists(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT) > 0)
        {
            H5Ldelete(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT);
        }
        
        // 创建复合数据类型，每个关节有位置和速度两个值
        hid_t JointType = H5Tcreate(H5T_COMPOUND, sizeof(double) * 2);
        if (JointType <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create joint compound type"));
            PrintErrorStack();
            H5Gclose(GroupID);
            return;
        }
        
        herr_t Status = H5Tinsert(JointType, "position", 0, NativeDoubleType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert position field in joint type"));
            PrintErrorStack();
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(JointType, "velocity", sizeof(double), NativeDoubleType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert velocity field in joint type"));
            PrintErrorStack();
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        // 创建数据空间
        const hsize_t Dims[1] = {static_cast<hsize_t>(JointCount)};
        hid_t DataSpace = H5Screate_simple(1, Dims, nullptr);
        if (DataSpace <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create joint data space"));
            PrintErrorStack();
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        // 创建数据集
        hid_t DatasetID = H5Dcreate2(
            GroupID,
            DatasetNameUTF8.c_str(),
            JointType,
            DataSpace,
            H5P_DEFAULT,
            H5P_DEFAULT,
            H5P_DEFAULT
        );
        
        if (DatasetID <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create joint dataset: %s"), *FinalDatasetName);
            PrintErrorStack();
            H5Sclose(DataSpace);
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        // 准备数据 - 将位置和速度组合成一个数组
        TArray<double> CombinedData;
        CombinedData.SetNum(JointCount * 2);
        
        for (int32 i = 0; i < JointCount; ++i)
        {
            CombinedData[i * 2] = JointData[i];        // 位置
            CombinedData[i * 2 + 1] = JointVelocities[i]; // 速度
        }
        
        // 写入数据
        Status = H5Dwrite(DatasetID, JointType, H5S_ALL, H5S_ALL, H5P_DEFAULT, CombinedData.GetData());
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to write joint dataset: %s"), *FinalDatasetName);
            PrintErrorStack();
            H5Dclose(DatasetID);
            H5Sclose(DataSpace);
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        // 添加关节名称属性
        hid_t NameSpace = H5Screate_simple(1, Dims, nullptr);
        if (NameSpace <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create name space for joint names"));
            PrintErrorStack();
            H5Dclose(DatasetID);
            H5Sclose(DataSpace);
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        // 将FString数组转换为C字符串数组
        TArray<const char*> CStrings;
        CStrings.Reserve(JointCount);
        for (const FString& Name : JointNames)
        {
            CStrings.Add(TCHAR_TO_ANSI(*Name));
        }
        
        // 创建并写入关节名称属性
        hid_t NamesAttr = H5Acreate2(DatasetID, "JointNames", NativeStringType, NameSpace, H5P_DEFAULT, H5P_DEFAULT);
        if (NamesAttr <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create JointNames attribute"));
            PrintErrorStack();
            H5Sclose(NameSpace);
            H5Dclose(DatasetID);
            H5Sclose(DataSpace);
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Awrite(NamesAttr, NativeStringType, CStrings.GetData());
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to write JointNames attribute"));
            PrintErrorStack();
            H5Aclose(NamesAttr);
            H5Sclose(NameSpace);
            H5Dclose(DatasetID);
            H5Sclose(DataSpace);
            H5Tclose(JointType);
            H5Gclose(GroupID);
            return;
        }
        
        // 关闭资源
        H5Aclose(NamesAttr);
        H5Sclose(NameSpace);
        H5Dclose(DatasetID);
        H5Sclose(DataSpace);
        H5Tclose(JointType);
        H5Gclose(GroupID);
    });
}

void UC_HDF5Util::WriteOdometryToHDF5(const FString& OdometryGroup, const FVector& Position, const FQuat& Rotation, const FVector& LinearVelocity, const FVector& AngularVelocity)
{
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [OdometryGroup, Position, Rotation, LinearVelocity, AngularVelocity]()
    {
    	FString RegularGroupName = GetRegularGroupName(OdometryGroup);
        FScopeLock ScopeLock(&WriteFileCriticalSection);
        if (H5FileHandle <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("H5FileHandle is invalid: %lld"), static_cast<long long>(H5FileHandle));
            return;
        }

        // 创建或打开里程计组
        hid_t GroupID;
        if (H5Lexists(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT) <= 0)
        {
            GroupID = H5Gcreate2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
            if (GroupID <= 0)
            {
                UE_LOG(LogHDF5, Error, TEXT("Failed to create odometry group: %s"), *RegularGroupName);
                PrintErrorStack();
                return;
            }
        }
        else
        {
            GroupID = H5Gopen2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT);
            if (GroupID <= 0)
            {
                UE_LOG(LogHDF5, Error, TEXT("Failed to open odometry group: %s"), *RegularGroupName);
                PrintErrorStack();
                return;
            }
        }

        // 使用时间戳作为数据集名称
        static int32 Counter = 0;
        FDateTime Now = FDateTime::Now();
        const FString FinalDatasetName = FString::Printf(TEXT("%d_%d_%d_%d"), Now.GetHour(), Now.GetMinute(), Now.GetSecond(), ++Counter);
        const std::string DatasetNameUTF8 = TCHAR_TO_UTF8(*FinalDatasetName);
        if (H5Lexists(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT) > 0)
        {
            H5Ldelete(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT);
        }
        
        // 创建复合数据类型
        hid_t OdometryType = H5Tcreate(H5T_COMPOUND, sizeof(float) * 13);
        if (OdometryType <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create odometry compound type"));
            PrintErrorStack();
            H5Gclose(GroupID);
            return;
        }
        
        // 添加位置字段 (3个float)
        herr_t Status = H5Tinsert(OdometryType, "position_x", 0 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert position_x field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "position_y", 1 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert position_y field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "position_z", 2 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert position_z field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 添加旋转字段 (4个float)
        Status = H5Tinsert(OdometryType, "rotation_x", 3 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert rotation_x field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "rotation_y", 4 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert rotation_y field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "rotation_z", 5 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert rotation_z field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "rotation_w", 6 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert rotation_w field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 添加线性速度字段 (3个float)
        Status = H5Tinsert(OdometryType, "linear_velocity_x", 7 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert linear_velocity_x field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "linear_velocity_y", 8 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert linear_velocity_y field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "linear_velocity_z", 9 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert linear_velocity_z field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 添加角速度字段 (3个float)
        Status = H5Tinsert(OdometryType, "angular_velocity_x", 10 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert angular_velocity_x field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "angular_velocity_y", 11 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert angular_velocity_y field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(OdometryType, "angular_velocity_z", 12 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert angular_velocity_z field"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 创建数据空间
        hid_t DataSpace = H5Screate(H5S_SCALAR);
        if (DataSpace <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create odometry data space"));
            PrintErrorStack();
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 创建数据集
        hid_t Dataset = H5Dcreate2(GroupID, DatasetNameUTF8.c_str(), OdometryType, DataSpace, 
                                  H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
        
        if (Dataset <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create odometry dataset: %s"), *FinalDatasetName);
            PrintErrorStack();
            H5Sclose(DataSpace);
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 准备数据
        float OdometryData[13];
        OdometryData[0] = Position.X;
        OdometryData[1] = Position.Y;
        OdometryData[2] = Position.Z;
        
        OdometryData[3] = Rotation.X;
        OdometryData[4] = Rotation.Y;
        OdometryData[5] = Rotation.Z;
        OdometryData[6] = Rotation.W;
        
        OdometryData[7] = LinearVelocity.X;
        OdometryData[8] = LinearVelocity.Y;
        OdometryData[9] = LinearVelocity.Z;
        
        OdometryData[10] = AngularVelocity.X;
        OdometryData[11] = AngularVelocity.Y;
        OdometryData[12] = AngularVelocity.Z;
        
        // 写入数据
        Status = H5Dwrite(Dataset, OdometryType, H5S_ALL, H5S_ALL, H5P_DEFAULT, OdometryData);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to write odometry dataset: %s"), *FinalDatasetName);
            PrintErrorStack();
            H5Dclose(Dataset);
            H5Sclose(DataSpace);
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 添加时间戳属性
        hid_t AttrSpace = H5Screate(H5S_SCALAR);
        if (AttrSpace <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create timestamp attribute space"));
            PrintErrorStack();
            H5Dclose(Dataset);
            H5Sclose(DataSpace);
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        hid_t TimeAttr = H5Acreate2(Dataset, "timestamp", NativeDoubleType, AttrSpace, H5P_DEFAULT, H5P_DEFAULT);
        if (TimeAttr <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create timestamp attribute"));
            PrintErrorStack();
            H5Sclose(AttrSpace);
            H5Dclose(Dataset);
            H5Sclose(DataSpace);
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        // 获取当前时间戳作为数值
        double Timestamp = FDateTime::Now().ToUnixTimestamp();
        Status = H5Awrite(TimeAttr, NativeDoubleType, &Timestamp);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to write timestamp attribute"));
            PrintErrorStack();
            H5Aclose(TimeAttr);
            H5Sclose(AttrSpace);
            H5Dclose(Dataset);
            H5Sclose(DataSpace);
            H5Tclose(OdometryType);
            H5Gclose(GroupID);
            return;
        }
        
        H5Aclose(TimeAttr);
        H5Sclose(AttrSpace);
        
        // 关闭数据集
        H5Dclose(Dataset);
        
        // 关闭资源
        H5Sclose(DataSpace);
        H5Tclose(OdometryType);
        H5Gclose(GroupID);
    });
}

void UC_HDF5Util::WriteIMUDataToHDF5(const FString& IMUGroup, const FQuat& Orientation, const FVector& AngularVelocity, const FVector& LinearAcceleration)
{
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [IMUGroup, Orientation, AngularVelocity, LinearAcceleration]()
    {
    	FString RegularGroupName = GetRegularGroupName(IMUGroup);
        FScopeLock ScopeLock(&WriteFileCriticalSection);
        if (H5FileHandle <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("H5FileHandle is invalid: %lld"), static_cast<long long>(H5FileHandle));
            return;
        }

        // 创建或打开IMU组
        hid_t GroupID;
        if (H5Lexists(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT) <= 0)
        {
            GroupID = H5Gcreate2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
            if (GroupID <= 0)
            {
                UE_LOG(LogHDF5, Error, TEXT("Failed to create IMU group: %s"), *RegularGroupName);
                PrintErrorStack();
                return;
            }
        }
        else
        {
            GroupID = H5Gopen2(H5FileHandle, TCHAR_TO_ANSI(*RegularGroupName), H5P_DEFAULT);
            if (GroupID <= 0)
            {
                UE_LOG(LogHDF5, Error, TEXT("Failed to open IMU group: %s"), *RegularGroupName);
                PrintErrorStack();
                return;
            }
        }

        // 使用当前时间作为数据集名称
        static int32 Counter = 0;
        FDateTime Now = FDateTime::Now();
        const FString FinalDatasetName = FString::Printf(TEXT("%d_%d_%d_%d"), Now.GetHour(), Now.GetMinute(), Now.GetSecond(), ++Counter);
        const std::string DatasetNameUTF8 = TCHAR_TO_UTF8(*FinalDatasetName);
        if (H5Lexists(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT) > 0)
        {
            H5Ldelete(GroupID, DatasetNameUTF8.c_str(), H5P_DEFAULT);
        }
        
        // 创建复合数据类型
        hid_t IMUType = H5Tcreate(H5T_COMPOUND, sizeof(float) * 10);
        if (IMUType <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create IMU compound type"));
            PrintErrorStack();
            H5Gclose(GroupID);
            return;
        }
        
        // 添加方向字段 (四元数, 4个float)
        herr_t Status = H5Tinsert(IMUType, "orientation_x", 0 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert orientation_x field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(IMUType, "orientation_y", 1 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert orientation_y field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(IMUType, "orientation_z", 2 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert orientation_z field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(IMUType, "orientation_w", 3 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert orientation_w field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        // 添加角速度字段 (3个float, rad/s)
        Status = H5Tinsert(IMUType, "angular_velocity_x", 4 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert angular_velocity_x field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(IMUType, "angular_velocity_y", 5 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert angular_velocity_y field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(IMUType, "angular_velocity_z", 6 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert angular_velocity_z field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        // 添加线性加速度字段 (3个float, m/s^2)
        Status = H5Tinsert(IMUType, "linear_acceleration_x", 7 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert linear_acceleration_x field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(IMUType, "linear_acceleration_y", 8 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert linear_acceleration_y field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        Status = H5Tinsert(IMUType, "linear_acceleration_z", 9 * sizeof(float), NativeFloatType);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to insert linear_acceleration_z field"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        // 创建数据空间
        hid_t DataSpace = H5Screate(H5S_SCALAR);
        if (DataSpace <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create IMU data space"));
            PrintErrorStack();
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        // 创建数据集
        hid_t Dataset = H5Dcreate2(GroupID, DatasetNameUTF8.c_str(), IMUType, DataSpace, 
                                  H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
        
        if (Dataset <= 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to create IMU dataset: %s"), *FinalDatasetName);
            PrintErrorStack();
            H5Sclose(DataSpace);
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        // 准备数据
        float IMUData[10];
        IMUData[0] = Orientation.X;
        IMUData[1] = Orientation.Y;
        IMUData[2] = Orientation.Z;
        IMUData[3] = Orientation.W;
        
        IMUData[4] = AngularVelocity.X;
        IMUData[5] = AngularVelocity.Y;
        IMUData[6] = AngularVelocity.Z;
        
        IMUData[7] = LinearAcceleration.X;
        IMUData[8] = LinearAcceleration.Y;
        IMUData[9] = LinearAcceleration.Z;
        
        // 写入数据
        Status = H5Dwrite(Dataset, IMUType, H5S_ALL, H5S_ALL, H5P_DEFAULT, IMUData);
        if (Status < 0)
        {
            UE_LOG(LogHDF5, Error, TEXT("Failed to write IMU dataset: %s"), *FinalDatasetName);
            PrintErrorStack();
            H5Dclose(Dataset);
            H5Sclose(DataSpace);
            H5Tclose(IMUType);
            H5Gclose(GroupID);
            return;
        }
        
        // 关闭数据集
        H5Dclose(Dataset);
        
        // 关闭资源
        H5Sclose(DataSpace);
        H5Tclose(IMUType);
        H5Gclose(GroupID);
    });
}
