/****************************************************************************
**
** Copyright (C) 2016 The Qt Company Ltd.
** Contact: https://www.qt.io/licensing/
**
** This file is part of the QtGui module of the Qt Toolkit.
**
** $QT_BEGIN_LICENSE:LGPL$
** Commercial License Usage
** Licensees holding valid commercial Qt licenses may use this file in
** accordance with the commercial license agreement provided with the
** Software or, alternatively, in accordance with the terms contained in
** a written agreement between you and The Qt Company. For licensing terms
** and conditions see https://www.qt.io/terms-conditions. For further
** information use the contact form at https://www.qt.io/contact-us.
**
** GNU Lesser General Public License Usage
** Alternatively, this file may be used under the terms of the GNU Lesser
** General Public License version 3 as published by the Free Software
** Foundation and appearing in the file LICENSE.LGPL3 included in the
** packaging of this file. Please review the following information to
** ensure the GNU Lesser General Public License version 3 requirements
** will be met: https://www.gnu.org/licenses/lgpl-3.0.html.
**
** GNU General Public License Usage
** Alternatively, this file may be used under the terms of the GNU
** General Public License version 2.0 or (at your option) the GNU General
** Public license version 3 or any later version approved by the KDE Free
** Qt Foundation. The licenses are as published by the Free Software
** Foundation and appearing in the file LICENSE.GPL2 and LICENSE.GPL3
** included in the packaging of this file. Please review the following
** information to ensure the GNU General Public License requirements will
** be met: https://www.gnu.org/licenses/gpl-2.0.html and
** https://www.gnu.org/licenses/gpl-3.0.html.
**
** $QT_END_LICENSE$
**
****************************************************************************/

#ifndef QICON_H
#define QICON_H

#include <QtGui/qtguiglobal.h>
#include <QtCore/qsize.h>
#include <QtCore/qlist.h>
#include <QtGui/qpixmap.h>

QT_BEGIN_NAMESPACE


class QIconPrivate;
class QIconEngine;
class QPainter;

class Q_GUI_EXPORT QIcon
{
public:
	enum Mode { Normal, Disabled, Active, Selected };
	enum State { On, Off };

	QIcon() noexcept;
	QIcon(const QPixmap& pixmap);
	QIcon(const QIcon& other);
	QIcon(QIcon&& other) noexcept
		: d(other.d)
	{
		other.d = nullptr;
	}
	explicit QIcon(const QString& fileName); // file or resource name
	explicit QIcon(QIconEngine* engine);
	~QIcon();
	QIcon& operator=(const QIcon& other);
	inline QIcon& operator=(QIcon&& other) noexcept
	{
		swap(other); return *this;
	}
	inline void swap(QIcon& other) noexcept
	{
		qSwap(d, other.d);
	}

	operator QVariant() const;

	QPixmap pixmap(const QSize& size, Mode mode = Normal, State state = Off) const;
	inline QPixmap pixmap(int w, int h, Mode mode = Normal, State state = Off) const
	{
		return pixmap(QSize(w, h), mode, state);
	}
	inline QPixmap pixmap(int extent, Mode mode = Normal, State state = Off) const
	{
		return pixmap(QSize(extent, extent), mode, state);
	}
	QPixmap pixmap(QWindow* window, const QSize& size, Mode mode = Normal, State state = Off) const;

	QSize actualSize(const QSize& size, Mode mode = Normal, State state = Off) const;
	QSize actualSize(QWindow* window, const QSize& size, Mode mode = Normal, State state = Off) const;

	QString name() const;

	void paint(QPainter* painter, const QRect& rect, Qt::Alignment alignment = Qt::AlignCenter, Mode mode = Normal, State state = Off) const;
	inline void paint(QPainter* painter, int x, int y, int w, int h, Qt::Alignment alignment = Qt::AlignCenter, Mode mode = Normal, State state = Off) const
	{
		paint(painter, QRect(x, y, w, h), alignment, mode, state);
	}

	bool isNull() const;
	bool isDetached() const;
	void detach();

#if QT_DEPRECATED_SINCE(5, 0)
	QT_DEPRECATED inline int serialNumber() const { return cacheKey() >> 32; }
#endif
	qint64 cacheKey() const;

	void addPixmap(const QPixmap& pixmap, Mode mode = Normal, State state = Off);
	void addFile(const QString& fileName, const QSize& size = QSize(), Mode mode = Normal, State state = Off);

	QList<QSize> availableSizes(Mode mode = Normal, State state = Off) const;

	void setIsMask(bool isMask);
	bool isMask() const;

	static QIcon fromTheme(const QString& name);
	static QIcon fromTheme(const QString& name, const QIcon& fallback);
	static bool hasThemeIcon(const QString& name);

	static QStringList themeSearchPaths();
	static void setThemeSearchPaths(const QStringList& searchpath);

	static QStringList fallbackSearchPaths();
	static void setFallbackSearchPaths(const QStringList& paths);

	static QString themeName();
	static void setThemeName(const QString& path);

	static QString fallbackThemeName();
	static void setFallbackThemeName(const QString& name);

	Q_DUMMY_COMPARISON_OPERATOR(QIcon)

private:
	QIconPrivate* d;
#if !defined(QT_NO_DATASTREAM)
	friend Q_GUI_EXPORT QDataStream& operator<<(QDataStream&, const QIcon&);
	friend Q_GUI_EXPORT QDataStream& operator>>(QDataStream&, QIcon&);
#endif

public:
	typedef QIconPrivate* DataPtr;
	inline DataPtr& data_ptr() { return d; }
};

Q_DECLARE_SHARED(QIcon)

#if !defined(QT_NO_DATASTREAM)
Q_GUI_EXPORT QDataStream& operator<<(QDataStream&, const QIcon&);
Q_GUI_EXPORT QDataStream& operator>>(QDataStream&, QIcon&);
#endif

#ifndef QT_NO_DEBUG_STREAM
Q_GUI_EXPORT QDebug operator<<(QDebug dbg, const QIcon&);
#endif

Q_GUI_EXPORT QString qt_findAtNxFile(const QString& baseFileName, qreal targetDevicePixelRatio,
	qreal* sourceDevicePixelRatio = nullptr);

QT_END_NAMESPACE

#endif // QICON_H
