using System.IO;
using Internal;
using UnrealBuildTool;

public class QT : ModuleRules
{
    public QT(ReadOnlyTargetRules Target) : base(Target)
    {
        Type = ModuleType.External;
        
        PublicDefinitions.Add("QT_NO_VERSION_TAGGING");
        // windows64平台
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            // 添加QT相关头文件，版本5.14.2
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows", "QtSql"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows", "QtXml"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows", "ActiveQt"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows", "QtGui"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows", "QtWidgets"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows", "QtCore"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Windows", "QtSerialPort"));

            // 添加QT编译时的链接静态库，版本5.14.2
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5Core.lib"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5AxContainer.lib"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5AxBase.lib"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5Gui.lib"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5Sql.lib"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5Widgets.lib"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5Xml.lib"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "X64", "Qt5SerialPort.lib"));

            //linux wps库仅测试用
            // PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "WPS", "common"));
            // PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "WPS","wps"));

            // 复制PostgreSQL运行时需要用到的动态库，版本15.2
            RuntimeDependencies.Add("$(BinaryOutputDir)/libwinpthread-1.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/libwinpthread-1.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libcrypto-3-x64.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/libcrypto-3-x64.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libiconv-2.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/libiconv-2.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libintl-9.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/libintl-9.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libpq.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/libpq.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libssl-3-x64.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/libssl-3-x64.dll");
            // 复制MySQL运行时需要用到的动态库，版本  (暂时不加)
            //RuntimeDependencies.Add("$(BinaryOutputDir)/libmysql.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/libmysql.dll");

            // 复制QT运行时需要用到的动态库，版本5.14.2
            RuntimeDependencies.Add("$(BinaryOutputDir)/Qt5Core.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/Qt5Core.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/Qt5Sql.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/Qt5Sql.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/Qt5Xml.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/Qt5Xml.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/Qt5Gui.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/Qt5Gui.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/Qt5Widgets.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/Qt5Widgets.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/Qt5SerialPort.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/QT5SerialPort.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/plugins/sqldrivers/qsqlpsql.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/plugins/sqldrivers/qsqlpsql.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/plugins/sqldrivers/qsqlite.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/plugins/sqldrivers/qsqlite.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/plugins/sqldrivers/qsqlodbc.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/plugins/sqldrivers/qsqlodbc.dll");
            RuntimeDependencies.Add("$(BinaryOutputDir)/plugins/sqldrivers/qsqlmysql.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/plugins/sqldrivers/qsqlmysql.dll");
            RuntimeDependencies.Add("$(TargetOutputDir)/platforms/qdirect2d.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/platforms/qdirect2d.dll");
            RuntimeDependencies.Add("$(TargetOutputDir)/platforms/qminimal.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/platforms/qminimal.dll");
            RuntimeDependencies.Add("$(TargetOutputDir)/platforms/qoffscreen.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/platforms/qoffscreen.dll");
            RuntimeDependencies.Add("$(TargetOutputDir)/platforms/qwindows.dll", "$(PluginDir)/Binaries/ThirdParty/Qt/Win64/platforms/qwindows.dll");


            // 在编辑器运行时，由于QT特性需要将QT的platforms插件目录放置于exe执行文件同级目录，版本5.14.2
            if (Target.bBuildEditor)
            {
                string EnginePath = Path.Combine(Target.RelativeEnginePath, "Binaries", "Win64", "platforms");
                if (!Directory.Exists(EnginePath))
                {
                    Directory.CreateDirectory(EnginePath);
                }
                File.Copy(Path.Combine(ModuleDirectory, "../", "../", "../", "Binaries", "ThirdParty", "QT", "Win64", "platforms", "qdirect2d.dll"),
                    Path.Combine(Target.RelativeEnginePath, "Binaries", "Win64", "platforms", "qdirect2d.dll"), true);
                File.Copy(Path.Combine(ModuleDirectory, "../", "../", "../", "Binaries", "ThirdParty", "QT", "Win64", "platforms", "qminimal.dll"),
                    Path.Combine(Target.RelativeEnginePath, "Binaries", "Win64", "platforms", "qminimal.dll"), true);
                File.Copy(Path.Combine(ModuleDirectory, "../", "../", "../", "Binaries", "ThirdParty", "QT", "Win64", "platforms", "qoffscreen.dll"),
                    Path.Combine(Target.RelativeEnginePath, "Binaries", "Win64", "platforms", "qoffscreen.dll"), true);
                File.Copy(Path.Combine(ModuleDirectory, "../", "../", "../", "Binaries", "ThirdParty", "QT", "Win64", "platforms", "qwindows.dll"),
                    Path.Combine(Target.RelativeEnginePath, "Binaries", "Win64", "platforms", "qwindows.dll"), true);

            }
        }
        else if (Target.Platform == UnrealTargetPlatform.Linux)
        {
            // 添加QT相关头文件，版本5.14.2
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "QtSql"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "QtXml"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "QtGui"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "QtWidgets"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "QtCore"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "QtSerialPort"));


            // 添加wps相关头文件
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "WPS", "common"));
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Include", "Linux", "WPS","wps"));
            // 添加QT编译时的链接静态库，版本5.14.2
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "Linux", "libQt5Core.so"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "Linux", "libQt5Gui.so"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "Linux", "libQt5SerialPort.so"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "Linux", "libQt5Sql.so"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "Linux", "libQt5Widgets.so"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Lib", "Linux", "libQt5Xml.so"));

            // TODO 复制PostgreSQL运行时需要用到的动态库，版本15.2

            // 复制QT运行时需要用到的动态库，版本5.14.2
            RuntimeDependencies.Add("$(BinaryOutputDir)/libQt5Core.so", "$(PluginDir)/Binaries/ThirdParty/QT/Linux/libQt5Core.so");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libQt5Sql.so", "$(PluginDir)/Binaries/ThirdParty/QT//Linux/libQt5Sql.so");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libQt5Xml.so", "$(PluginDir)/Binaries/ThirdParty/QT//Linux/libQt5Xml.so");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libQt5Gui.so", "$(PluginDir)/Binaries/ThirdParty/QT//Linux/libQt5Gui.so");
            RuntimeDependencies.Add("$(BinaryOutputDir)/libQt5Widgets.so", "$(PluginDir)/Binaries/ThirdParty/QT//Linux/libQt5Widgets.so");
            RuntimeDependencies.Add("$(BinaryOutputDir)/ibQt5SerialPort.so", "$(PluginDir)/Binaries/ThirdParty/QT//Linux/libQt5SerialPort.so");
            
        }
    }
}
