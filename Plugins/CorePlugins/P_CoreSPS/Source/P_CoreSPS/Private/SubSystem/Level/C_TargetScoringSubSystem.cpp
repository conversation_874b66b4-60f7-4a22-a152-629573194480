// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利


#include "SubSystem/Level/C_TargetScoringSubSystem.h"

#include "C_DataAccessBPLibrary.h"
#include "C_UdpReceiver.h"
#include "DebugLogLibrarySettings.h"
#include "BaseEntity/Environment/C_BaseTarget.h"
#include "BaseEntity/Person/C_HumanoidRobot.h"
#include "Component/EntityComponent/Target/C_TargetRingComponent.h"
#include "Component/EntityComponent/Weapon/C_BaseWeaponComponent.h"
#include "Component/EntityComponent/Weapon/C_LaserWeaponComponent.h"
#include "DataStruct/C_ShootingRecord.h"
#include "DataStruct/C_TargetType.h"
#include "GameFramework/GameMode.h"
#include "GamePlay/C_GameInstanceSPS.h"
#include "GamePlay/C_GameModeSPS.h"
#include "Kismet/GameplayStatics.h"
#include "SubSystem/Engine/C_LogSubsystem.h"

UC_TargetScoringSubSystem* UC_TargetScoringSubSystem::Get()
{
	const UObject* WorldContextObject = GWorld;
	UWorld* world = GEngine->GetWorldFromContextObject(WorldContextObject, EGetWorldErrorMode::LogAndReturnNull);
	return world ? world->GetSubsystem<UC_TargetScoringSubSystem>() : nullptr;
}

bool UC_TargetScoringSubSystem::ShouldCreateSubsystem(UObject* Outer) const
{
	if (!Super::ShouldCreateSubsystem(Outer))
		return false;

	const UWorld* World = Cast<UWorld>(Outer);
	if (World == nullptr)
		return false;
	
	// 编辑器模式，并且不是在PIE模式下
	if (GIsEditor && World->WorldType != EWorldType::PIE)
		return false;

	return true;
}

void UC_TargetScoringSubSystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	InitRoundID();
	
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(this);
	if (GameInstance == nullptr)
		return;

	UC_GameInstanceSPS* GameInstanceSPS = Cast<UC_GameInstanceSPS>(GameInstance);
	if (GameInstanceSPS == nullptr || GameInstanceSPS->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
		return;

	// 获取打靶计分系统监听端口
	FString FilePath = FPaths::Combine(FPaths::ProjectConfigDir(), "User_BaseConfig.ini");
	bool bSuccess = false;
	UPRVariant* Value = UC_DataAccessBPLibrary::readIniAsVariant(FilePath, TEXT("TargetScore"), TEXT("listen_port"),
	                                                             bSuccess);
	int Port;
	if (!bSuccess)
	{
		Port = 9200;
		UC_LogSubsystem::Get()->RunMessage(TEXT("get port of target score system failed, use default value 9200"));
	}
	else
	{
		 Port = Value->toInt();
	}

	// 初始化udp socket
	ExternalSystemReceiver = NewObject<UC_UdpReceiver>(this);
	ExternalSystemReceiver->AddToRoot();
	ExternalSystemReceiver->InitReceiver("TargetScoringSystemReceiver", Port);
	ExternalSystemReceiver->OnDataReceived.AddDynamic(this, &UC_TargetScoringSubSystem::OnDataReceived);
}

void UC_TargetScoringSubSystem::Deinitialize()
{
	if (ExternalSystemReceiver != nullptr)
	{
		ExternalSystemReceiver->RemoveFromRoot();
		ExternalSystemReceiver->MarkAsGarbage();
		ExternalSystemReceiver = nullptr;
	}
	
	Super::Deinitialize();
}

void UC_TargetScoringSubSystem::NotifyFireResult(const UC_BaseWeaponComponent* Weapon, const FHitResult& HitResult)
{
	// 非训练模式不接受武器的结果通知
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(Weapon);
	if (GameInstance == nullptr)
		return;
	UC_GameInstanceSPS* GameInstanceSPS = Cast<UC_GameInstanceSPS>(GameInstance);
	// if (GameInstanceSPS == nullptr || GameInstanceSPS->GetBusinessUsageModel() != EBusinessUsageModel::Training)
	// 	return;
	
	if (Weapon == nullptr)
		return;

	const UC_LaserWeaponComponent* LaserWeaponComponent = Cast<UC_LaserWeaponComponent>(Weapon);
	if (LaserWeaponComponent == nullptr)
		return;

	AActor* Owner = Weapon->GetOwner();
	if (Owner == nullptr)
		return;

	AC_HumanoidRobot* HumanoidRobot = Cast<AC_HumanoidRobot>(Owner);
	if (HumanoidRobot == nullptr)
		return;

	FShootingRecord ShootingRecord;
	ShootingRecord.RoundID = CurrentRoundID;
	ShootingRecord.RobotID = HumanoidRobot->GetRobotID();
	ShootingRecord.WeaponID = LaserWeaponComponent->GetDeviceID();
	ShootingRecord.DateTime = FDateTime::Now();

	if (!HitResult.bBlockingHit)
	{
		ShootingRecord.Score = 0;
	}
	else
	{
		AActor* HitActor = HitResult.GetActor();
		if (HitActor == nullptr)
		{
			ShootingRecord.Score = 0;
		}
		else
		{
			AC_BaseTarget* BaseTarget = Cast<AC_BaseTarget>(HitActor);
			if (BaseTarget == nullptr)
			{
				ShootingRecord.Score = 0;
			}
			else
			{
				ShootingRecord.TargetType = BaseTarget->GetTargetType();
				ShootingRecord.TargetID = BaseTarget->GetTargetID();
				if (ShootingRecord.TargetType == ETargetType::HumanoidTarget)
				{
					if (BaseTarget->GetShouldAvoid())
					{
						ShootingRecord.Score = -1;
					}
					else
					{
						ShootingRecord.Score = 1;
					}
				}
				else
				{
					UPrimitiveComponent* PrimitiveComponent = HitResult.GetComponent();
					UC_TargetRingComponent* TargetRingComponent = Cast<UC_TargetRingComponent>(PrimitiveComponent);
					if (TargetRingComponent == nullptr)
					{
						ShootingRecord.Score = 0;
					}
					else
					{
						ShootingRecord.Score = TargetRingComponent->GetRingNum();
					}
				}
			}
		}
	}
	if (WeaponFireSequences.Contains(ShootingRecord.WeaponID))
	{
		ShootingRecord.FireSequenceNum = WeaponFireSequences[ShootingRecord.WeaponID] + 1;
		WeaponFireSequences[ShootingRecord.WeaponID] = ShootingRecord.FireSequenceNum;
	}
	else
	{
		ShootingRecord.FireSequenceNum = 1;
		WeaponFireSequences.Add(ShootingRecord.WeaponID, ShootingRecord.FireSequenceNum);
	}
	CreateShootingRecord(ShootingRecord);
	OnNewShootingRecordCreated.Broadcast(ShootingRecord);
}

bool UC_TargetScoringSubSystem::IsTargetPracticeMode() const
{
	// 判断GameMode是不是处于打靶场景，是不是正在运行仿真
	AGameModeBase* GameModeBase = UGameplayStatics::GetGameMode(this);
	if (GameModeBase == nullptr)
		return false;
	AC_GameModeSPS *GameModeSPS = Cast<AC_GameModeSPS>(GameModeBase);
	if (GameModeSPS == nullptr || GameModeSPS->GetBusinessScene() != EBusinessScene::TargetShooting || !GameModeSPS->IsPlayGame())
		return false;

	// 判断GameInstance是不是处于监控或巡逻模式，如果不是则返回false
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(this);
	if (GameInstance == nullptr)
		return false;

	UC_GameInstanceSPS* GameInstanceSPS = Cast<UC_GameInstanceSPS>(GameInstance);
	if (GameInstanceSPS == nullptr || GameInstanceSPS->GetBusinessUsageModel() == EBusinessUsageModel::UnKnown)
		return false;

	return true;
}

void UC_TargetScoringSubSystem::OnDataReceived(const TArray<uint8>& RawData, const FString& IP, const int& Port)
{
	// 非监控模式不接受打靶计分系统通知
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(this);
	if (GameInstance == nullptr)
		return;
	UC_GameInstanceSPS* GameInstanceSPS = Cast<UC_GameInstanceSPS>(GameInstance);
	if (GameInstanceSPS == nullptr || GameInstanceSPS->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
		return;
	
	UC_LogSubsystem* LogSubsystem = UC_LogSubsystem::Get();
	const FString JsonStr = StringCast<TCHAR>(reinterpret_cast<const ANSICHAR*>(RawData.GetData()), RawData.Num()).Get();
	
	FString LogMsg = FString::Printf(TEXT("receive target scoring system[%s:%d] info:%s"), *IP, Port, *JsonStr);
	LogSubsystem->RunMessage(LogMsg, DL_Info);
	TSharedPtr<FJsonObject> JsonObject;
	if (!FJsonSerializer::Deserialize(TJsonReaderFactory<>::Create(JsonStr), JsonObject))
	{
		LogMsg = TEXT("convert to json object failed");
		UC_LogSubsystem::Get()->RunMessage(LogMsg, DL_Warning);
		return;
	}

	FShootingRecord ShootingRecord;
	if (!ConvertToShootingRecord(JsonObject, ShootingRecord))
	{
		LogMsg = TEXT("convert to shooting record struct failed");
		UC_LogSubsystem::Get()->RunMessage(LogMsg, DL_Warning);
		return;
	}

	CreateShootingRecord(ShootingRecord);
	OnNewShootingRecordCreated.Broadcast(ShootingRecord);
}

bool UC_TargetScoringSubSystem::ConvertToShootingRecord(const TSharedPtr<FJsonObject>& JsonObj, FShootingRecord& ShootingRecord)
{
	if (!JsonObj.IsValid())
		return false;

	int IntField;
	bool bSuccess = JsonObj->TryGetNumberField(TEXT("gunNum"), IntField);
	if (!bSuccess)
		return false;
	ShootingRecord.WeaponID = IntField;

	if (WeaponFireSequences.Contains(ShootingRecord.WeaponID))
	{
		ShootingRecord.FireSequenceNum = WeaponFireSequences[ShootingRecord.WeaponID] + 1;
		WeaponFireSequences[ShootingRecord.WeaponID] = ShootingRecord.FireSequenceNum;
	}
	else
	{
		ShootingRecord.FireSequenceNum = 1;
		WeaponFireSequences.Add(ShootingRecord.WeaponID, ShootingRecord.FireSequenceNum);
	}
	
	bSuccess = JsonObj->TryGetNumberField(TEXT("targetNum"), IntField);
	if (!bSuccess)
		return false;
	ShootingRecord.TargetID = IntField;

	FString StrField;
	bSuccess = JsonObj->TryGetStringField(TEXT("targetType"), StrField);
	if (!bSuccess)
		return false;
	if (StrField == TEXT("胸环靶"))
		ShootingRecord.TargetType = ETargetType::BullseyeTarget;
	else if (StrField == TEXT("人型靶"))
		ShootingRecord.TargetType = ETargetType::HumanoidTarget;
	else
		return false;

	float floatField;
	bSuccess = JsonObj->TryGetNumberField(TEXT("ringNum"), floatField);
	if (!bSuccess)
		return false;
	ShootingRecord.Score = floatField;

	bSuccess = JsonObj->TryGetStringField(TEXT("time"), StrField);
	if (!bSuccess)
		return false;
	bSuccess = FDateTime::ParseIso8601(*StrField, ShootingRecord.DateTime);
	if (!bSuccess)
		return false;

	AC_HumanoidRobot* WeaponOwner = FindWeaponOwner(ShootingRecord.WeaponID);
	if (WeaponOwner == nullptr)
		return false;
	ShootingRecord.RobotID = WeaponOwner->GetRobotID();
	ShootingRecord.RoundID = CurrentRoundID;
	
	return true;
}

void UC_TargetScoringSubSystem::CreateShootingRecord(const FShootingRecord& ShootingRecord) const
{
	const FString InsertSql = "INSERT INTO shooting_record (round_id, robot_id, weapon_id, sequence_num, target_id, target_type, score, time, sim_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
	int QueryID;
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(this);
	if (GameInstance == nullptr)
		return;

	UC_GameInstanceSPS* GameInstanceSps = Cast<UC_GameInstanceSPS>(GameInstance);
	if (GameInstanceSps == nullptr)
		return;
	
	UC_DataAccessBPLibrary::prepare(InsertSql, QueryID);
	UC_DataAccessBPLibrary::bindValueInt(0, ShootingRecord.RoundID, QueryID);
	UC_DataAccessBPLibrary::bindValueInt(1, ShootingRecord.RobotID, QueryID);
	UC_DataAccessBPLibrary::bindValueInt(2, ShootingRecord.WeaponID, QueryID);
	UC_DataAccessBPLibrary::bindValueInt(3, ShootingRecord.FireSequenceNum, QueryID);
	UC_DataAccessBPLibrary::bindValueInt(4, ShootingRecord.TargetID, QueryID);
	UC_DataAccessBPLibrary::bindValueInt(5, static_cast<int>(ShootingRecord.TargetType), QueryID);
	UC_DataAccessBPLibrary::bindValueDouble(6, ShootingRecord.Score, QueryID);
	UC_DataAccessBPLibrary::bindValueDateTime(7, ShootingRecord.DateTime, QueryID);
	UC_DataAccessBPLibrary::bindValueInt64(8, GameInstanceSps->CurrentSim.ID, QueryID);

	const bool bSuccess = UC_DataAccessBPLibrary::execPrepare(QueryID);
	if (!bSuccess)
	{
		UC_LogSubsystem::Get()->RunMessage(TEXT("insert data into shooting_record failed."));
	}
}

AC_HumanoidRobot* UC_TargetScoringSubSystem::FindWeaponOwner(int WeaponID) const
{
	TArray<AActor*> HumanoidRobots;
	UGameplayStatics::GetAllActorsOfClass(this, AC_HumanoidRobot::StaticClass(), HumanoidRobots);
	for(const auto Actor: HumanoidRobots)
	{
		if (Actor == nullptr)
			continue;

		AC_HumanoidRobot* HumanoidRobot = Cast<AC_HumanoidRobot>(Actor);
		if (HumanoidRobot == nullptr)
			continue;

		if (HumanoidRobot->ContainWeapon(WeaponID))
			return HumanoidRobot;
	}

	return nullptr;
}

void UC_TargetScoringSubSystem::InitRoundID()
{
	FString QuerySql = TEXT("SELECT MAX(round_id) FROM shooting_record");
	int QueryID;
	bool bSuccess = UC_DataAccessBPLibrary::execQuery(QuerySql, QueryID);
	if (!bSuccess)
	{
		CurrentRoundID = -1;
		return;
	}

	while (UC_DataAccessBPLibrary::nextRecord(QueryID))
	{
		int MaxRoundID = UC_DataAccessBPLibrary::getValueAsInt(0, QueryID);
		CurrentRoundID = MaxRoundID + 1;
		return;
	}

	CurrentRoundID = 1;
	
}

