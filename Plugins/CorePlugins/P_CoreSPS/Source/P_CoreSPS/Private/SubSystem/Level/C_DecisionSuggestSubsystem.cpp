// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利


#include "SubSystem/Level/C_DecisionSuggestSubsystem.h"
#include "C_DataAccessBPLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Blueprint/UserWidget.h"

DECLARE_LOG_CATEGORY_EXTERN(SimulationStepSizeLog, Log, All);
DEFINE_LOG_CATEGORY(SimulationStepSizeLog);

bool UC_DecisionSuggestSubsystem::CheckStringIsValid(const FString& str, const FString Reg)
{
	//Reg=TEXT("[^a-zA-Z0-9]")
	//用于表示正则表达式的类
	FRegexPattern Pattern(Reg);
	//用于执行正则表达式匹配的类
	FRegexMatcher regMatcher(Pattern, str);
	//设置匹配器的搜索范围，从字符串的开始位置0到字符串的末尾位置str.Len()
	regMatcher.SetLimits(0, str.Len());
	//用于在字符串中查找下一个与正则表达式相匹配的位置。
	return regMatcher.FindNext();//true表示非法字符、false表示为字母或数字
}

void UC_DecisionSuggestSubsystem::OutSimulationSterLog()
{
	FString DeltaSecond = FString::SanitizeFloat((GetWorld()->GetDeltaSeconds()*1000.f));
	UE_LOG(SimulationStepSizeLog, Log, TEXT("系统仿真步长：%s毫秒"), *DeltaSecond);
}


UC_DecisionSuggestSubsystem* UC_DecisionSuggestSubsystem::Get()
{
	if(!GWorld) return nullptr;
	const UObject* WorldContextObject = GWorld;
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContextObject,EGetWorldErrorMode::LogAndReturnNull);
	return World?World->GetSubsystem<UC_DecisionSuggestSubsystem>():nullptr;
}

void UC_DecisionSuggestSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UC_DecisionSuggestSubsystem::Deinitialize()
{
	Super::Deinitialize();
}

bool UC_DecisionSuggestSubsystem::UpdateSnapshootSPS(const FString& TableName, const int64& id,
                                                     const TArray<uint8>& Data)
{
	FString sql = FString::Printf(TEXT("UPDATE %s SET snapshoot = (?) WHERE id='%lld' "),*TableName,id);
	int queryID;
	UC_DataAccessBPLibrary::prepare(sql,queryID);
	UE_LOG(LogTemp,Warning,TEXT("UpdateSnapshoot_sql: '%s' "),*sql);
	UC_DataAccessBPLibrary::bindValueByteArray(0, Data, queryID);
	return UC_DataAccessBPLibrary::execPrepare(queryID);
}

bool UC_DecisionSuggestSubsystem::GetSnapshootSPS(const FString& TableName, const int64& id, TArray<uint8>& Data)
{
	FString SelectSql = FString::Printf(TEXT("select Snapshoot from %s where id = '%lld' "),*TableName,id);
	UE_LOG(LogTemp,Warning,TEXT("GetSnapshoot_sql: '%s' "),*SelectSql);
	int queryID;
	UC_DataAccessBPLibrary::execQuery(SelectSql, queryID);
	if (UC_DataAccessBPLibrary::nextRecord(queryID))
	{
		Data = UC_DataAccessBPLibrary::getValueAsByteArray(0,queryID);
	
	}
	UC_DataAccessBPLibrary::closeQuery(queryID);
	if(Data.IsEmpty())
	{
		return false;
	}
	return true;
}

bool UC_DecisionSuggestSubsystem::UpdateOriginSql(const int64& id, const float& X, const float& Y,const float& Z)
{
	FString sql = FString::Printf(TEXT("UPDATE map SET origin_x = ?,origin_y = ?,origin_z = ?,origin_tag = ? WHERE id = '%lld' "),id);
	int queryID;
	UC_DataAccessBPLibrary::prepare(sql,queryID);
	UC_DataAccessBPLibrary::bindValueDouble(0, X, queryID);
	UC_DataAccessBPLibrary::bindValueDouble(1, Y, queryID);
	UC_DataAccessBPLibrary::bindValueDouble(2, Z, queryID);
	UC_DataAccessBPLibrary::bindValueInt(3, 1, queryID);
	return UC_DataAccessBPLibrary::execPrepare(queryID);
}

bool UC_DecisionSuggestSubsystem::GetOriginByDB(const int64& id, float& X, float& Y,float& Z)
{
	FString SelectSql = FString::Printf(TEXT("select origin_x,origin_y,origin_z,origin_tag from map where id = '%lld' "),id);
	int queryID;
	int OriginTag=false;
	UC_DataAccessBPLibrary::execQuery(SelectSql, queryID);
	if (UC_DataAccessBPLibrary::nextRecord(queryID))
	{
		X = UC_DataAccessBPLibrary::getValueAsDouble(0,queryID);
		Y = UC_DataAccessBPLibrary::getValueAsDouble(1,queryID);
		Z = UC_DataAccessBPLibrary::getValueAsDouble(2,queryID);
		OriginTag = UC_DataAccessBPLibrary::getValueAsInt(3,queryID);
	
	}
	UC_DataAccessBPLibrary::closeQuery(queryID);
	if(OriginTag==0)
	{
		bMemoryOriginIsNull=true;
		return false;
	}
	else if(OriginTag==1)
	{
		MemoryOrigin.X = X;
		MemoryOrigin.Y = Y;
		MemoryOrigin.Z = Z;
		bMemoryOriginIsNull=false;
		return true;
	}
	bMemoryOriginIsNull=true;
	return false;
}

bool UC_DecisionSuggestSubsystem::GetOriginByMemory(FVector& OutOrigin)
{
	OutOrigin=MemoryOrigin;
	return !bMemoryOriginIsNull;
}

void UC_DecisionSuggestSubsystem::SetOrigin(FVector InOrigin)
{
	MemoryOrigin=InOrigin;
	bMemoryOriginIsNull=false;
}


#pragma region Decision

/*-----------------------决策--------------------------*/

TArray<FStructDecision> UC_DecisionSuggestSubsystem::QueryDSData()
{
	FString Sql = FString::Printf(TEXT(
		"SELECT map.level_name,scenario_main.name,sim_main.start_time, "
		"sim_main.id, "
		"COALESCE(SUM(CASE WHEN alarm_recording.type = 0 THEN 1 ELSE 0 END), 0) AS count_type_0, "
		"COALESCE(SUM(CASE WHEN alarm_recording.type = 1 THEN 1 ELSE 0 END), 0) AS count_type_1, "
		"COALESCE(SUM(CASE WHEN alarm_recording.type = 2 THEN 1 ELSE 0 END), 0) AS count_type_2, "
		"scenario_main.type,sim_main.name "
		"FROM sim_main "
		"LEFT JOIN alarm_recording ON sim_main.id = alarm_recording.sim_id "
		"JOIN scenario_main ON sim_main.scenario_id = scenario_main.id "
		"JOIN map ON scenario_main.map_id = map.id "
		"GROUP BY sim_main.id, sim_main.start_time, map.level_name, scenario_main.name,scenario_main.type,\nsim_main.name "
		"ORDER BY sim_main.start_time DESC"));

	return QuerySql(Sql);
}


TArray<FStructDecision> UC_DecisionSuggestSubsystem::QueryDSDataByFilter(const FString& TrainName, const FString& TrainType, const FString& AssessSuggest, const FString& StartTime, const FString& EndTime)
{
	FString Type1 = TEXT("所有建议");

	if (TrainName.IsEmpty() && TrainType == NoType && AssessSuggest == Type1)
	{
		return QueryDSData();
	}

	FString Sql = FString::Printf(TEXT(
		"SELECT map.level_name,scenario_main.name,sim_main.start_time, "
		"sim_main.id, "
		"COALESCE(SUM(CASE WHEN alarm_recording.type = 0 THEN 1 ELSE 0 END), 0) AS count_type_0, "
		"COALESCE(SUM(CASE WHEN alarm_recording.type = 1 THEN 1 ELSE 0 END), 0) AS count_type_1, "
		"COALESCE(SUM(CASE WHEN alarm_recording.type = 2 THEN 1 ELSE 0 END), 0) AS count_type_2, "
		"scenario_main.type,sim_main.name "
		"FROM sim_main "
		"LEFT JOIN alarm_recording ON sim_main.id = alarm_recording.sim_id "
		"JOIN scenario_main ON sim_main.scenario_id = scenario_main.id "
		"JOIN map ON scenario_main.map_id = map.id "
		"WHERE 1=1 "));
		
	FString Group = TEXT(
		"GROUP BY sim_main.id, sim_main.start_time, map.level_name, scenario_main.name,scenario_main.type,\nsim_main.name "
		"ORDER BY sim_main.start_time DESC");

	FString ConditionName, ConditionType, ConditionSuggest;

	if (!TrainName.IsEmpty())
	{
		ConditionName = FString::Printf( TEXT(" AND sim_main.name like '%%%s%%' "),*TrainName);
		Sql += ConditionName;
	}
	if (TrainType != NoType)
	{
		ConditionType = FString::Printf(TEXT(" AND scenario_main.type = '%s' "), *TrainType);
		Sql += ConditionType;
	}

	/*if (AssessSuggest != Type1)
	{
		ConditionSuggest = FString::Printf(TEXT("  = '%s' "),*AssessSuggest);
	}*/

	Sql += FString::Printf(TEXT(" AND sim_main.start_time BETWEEN '%s' AND '%s' "), *StartTime, *EndTime);

	Sql += Group;

	return QuerySql(Sql);
}

TArray<FStructDecision> UC_DecisionSuggestSubsystem::QuerySql(const FString& Sql)
{
	TArray<FStructDecision> DecisionList;
	TArray<FString> SuggestList;
	SuggestList.Add(TEXT("不确定"));
	SuggestList.Add(TEXT("拒绝"));
	SuggestList.Add(TEXT("接受"));

	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{
		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			FStructDecision Decision;
			Decision.level_nameOfMap = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
			Decision.nameOfScenario_main = UC_DataAccessBPLibrary::getValueAsString(1, queryId);
			Decision.start_timeOfSim_main = UC_DataAccessBPLibrary::getValueAsString(2, queryId);
			Decision.simid = UC_DataAccessBPLibrary::getValueAsInt64(3, queryId);
			Decision.CountType0 = UC_DataAccessBPLibrary::getValueAsInt(4, queryId);
			Decision.CountType1 = UC_DataAccessBPLibrary::getValueAsInt(5, queryId);
			Decision.CountType2 = UC_DataAccessBPLibrary::getValueAsInt(6, queryId);
			Decision.Type = UC_DataAccessBPLibrary::getValueAsInt(7, queryId);
			Decision.nameOfSim_main = UC_DataAccessBPLibrary::getValueAsString(8, queryId);
			Decision.AssessSuggest = SuggestList[0];
			DecisionList.Add(Decision);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	//数据处理
	for (int i = 0; i < DecisionList.Num(); ++i)
	{
		//建议数据
		if (DecisionList[i].CountType0 > 1 || DecisionList[i].CountType1 > 3 || DecisionList[i].CountType2 > 0)
		{
			DecisionList[i].AssessSuggest = SuggestList[1];
		}
		else
		{
			DecisionList[i].AssessSuggest = SuggestList[2];
		}
		//百分比数据
		if (DecisionList[i].CountType0 != 0 || DecisionList[i].CountType1 != 0 || DecisionList[i].CountType2 != 0)
		{
			float Total = DecisionList[i].CountType0 + DecisionList[i].CountType1+ DecisionList[i].CountType2;
			float Type0 = DecisionList[i].CountType0 / Total * 100.f;
			float Type1 = DecisionList[i].CountType1 / Total * 100.f;
			float Type2 = DecisionList[i].CountType2 / Total * 100.f;

			FString TempString;
			TempString = FString::Printf(TEXT("%.2f"), Type0) + TEXT("%");
			DecisionList[i].CountType0Percent = TempString;

			TempString = FString::Printf(TEXT("%.2f"), Type1) + TEXT("%");
			DecisionList[i].CountType1Percent = TempString;

			TempString = FString::Printf(TEXT("%.2f"), Type2) + TEXT("%");
			DecisionList[i].CountType2Percent = TempString;
		}
		else
		{
			DecisionList[i].CountType0Percent = FString::Printf(TEXT("0"));
			DecisionList[i].CountType1Percent = FString::Printf(TEXT("0"));
			DecisionList[i].CountType2Percent = FString::Printf(TEXT("0"));
		}
	}
	UE_LOG(LogTemp, Warning, TEXT("DecisionSql:  %s"), *Sql);
	return DecisionList;
}

FString UC_DecisionSuggestSubsystem::GetTrainPath(const FString& TrainType)
{
	FString TrainTypePath = TEXT("所有类别");//决策建议的场景类别
	FString SceneTypePath = TEXT("全部");//
	if (TrainType == TrainTypePath ) 
	{
		return TrainTypePath;
	}
	if (TrainType == SceneTypePath)
	{
		return SceneTypePath;
	}
	FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>(TEXT("AssetRegistry"));
	// 遍历关卡
	TArray<FAssetData> AssetList;
	AssetRegistryModule.Get().GetAssetsByClass(UWorld::StaticClass()->GetClassPathName(), AssetList, true);

	for (FAssetData Asset : AssetList)
	{
		FString MapPath = Asset.PackageName.ToString();

		bool bIsContain = JudgeIsPatrolByMapPath(MapPath);
		if (bIsContain)
		{
			if (TrainType == TEXT("巡逻"))
			{
				TrainTypePath = GetFilterPath()[0];
				//UE_LOG(LogTemp, Warning, TEXT("MapPath: %s"), *MapPath);
				return TrainTypePath;
			}
			else if (TrainType == TEXT("打靶"))
			{
				TrainTypePath = GetFilterPath()[1];
				//UE_LOG(LogTemp, Warning, TEXT("MapPath: %s"), *MapPath);
				return TrainTypePath;
			}
		}
	}
	return TrainTypePath;
}

#pragma endregion

/*-----------------------场景管理--------------------------*/
#pragma region SceneManage
TArray<FStructSceneManage> UC_DecisionSuggestSubsystem::QuerySceneMapData()
{
	TArray<FStructSceneManage> SceneList;

	FString Sql = FString::Printf(TEXT(
		"SELECT map.name,map.create_time,map.level_name,map.id,map.type,snapshoot "
		"FROM map "
		"ORDER BY map.create_time DESC"));

	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{

		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			FStructSceneManage Scene;
			Scene.Name = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
			Scene.Time = UC_DataAccessBPLibrary::getValueAsString(1, queryId);
			Scene.LevelName = UC_DataAccessBPLibrary::getValueAsString(2, queryId);
			Scene.MapId= UC_DataAccessBPLibrary::getValueAsInt64(3, queryId);
			Scene.Type = UC_DataAccessBPLibrary::getValueAsInt(4, queryId);
			Scene.picture=UC_DataAccessBPLibrary::getValueAsByteArray(5,queryId);
			SceneList.Add(Scene);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	return SceneList;
}

FStructSceneManage UC_DecisionSuggestSubsystem::GetSceneDataById(const int64& id)
{
	FStructSceneManage SceneData;
	FString sqlInsert = FString::Printf(TEXT("SELECT id, name, type, modify_time, level_name, snapshoot FROM map WHERE id = '%lld';"), id);
	int quryid;

	if (UC_DataAccessBPLibrary::execQuery(sqlInsert, quryid))
	{
		while (UC_DataAccessBPLibrary::nextRecord(quryid))
		{
			SceneData.MapId = UC_DataAccessBPLibrary::getValueAsInt64(0, quryid);
			SceneData.Name = UC_DataAccessBPLibrary::getValueAsString(1, quryid);
			SceneData.Type = UC_DataAccessBPLibrary::getValueAsInt(2, quryid);
			SceneData.Time = UC_DataAccessBPLibrary::getValueAsString(3, quryid);
			SceneData.LevelName = UC_DataAccessBPLibrary::getValueAsString(4, quryid);
			SceneData.picture = UC_DataAccessBPLibrary::getValueAsByteArray(5, quryid);
		}

		UC_DataAccessBPLibrary::closeQuery(quryid);
	}

	return SceneData;
}

TArray<FStructSceneManage> UC_DecisionSuggestSubsystem::QuerySceneDataByFilter(const FString& SceneName, const FString& SceneType, const FString& StartTime, const FString& EndTime)
{


	//FString TrainPath = GetTrainPath(SceneType);

	FString Sql = FString::Printf(TEXT(
		"SELECT map.name,map.create_time,map.level_name,map.id,map.type,snapshoot "
		"FROM \"map\" "
		"WHERE 1=1 "));

	FString EndSql = TEXT("ORDER BY map.create_time DESC");

	if (SceneName.IsEmpty() && SceneType == NoType)
	{
		Sql += FString::Printf(TEXT(" AND map.create_time BETWEEN '%s' AND '%s' "), *StartTime, *EndTime);
		Sql += EndSql;
		UE_LOG(LogTemp, Warning, TEXT("SceneSql:  %s"), *Sql);
		return QuerySceneMap(Sql);
	}

	if (!SceneName.IsEmpty())
	{
		Sql += FString::Printf(TEXT("AND map.name like '%%%s%%' "), *SceneName);
		
	}
	if (SceneType != NoType)
	{
		Sql += FString::Printf(TEXT(" AND map.Type = '%s' "), *SceneType);
		
	}

	Sql+= FString::Printf(TEXT(" AND map.create_time BETWEEN '%s' AND '%s' "), *StartTime,*EndTime);
	
	Sql += EndSql;
	
	UE_LOG(LogTemp, Warning, TEXT("SceneSql:  %s"), *Sql);
	return QuerySceneMap(Sql);
}

TArray<FStructSceneManage> UC_DecisionSuggestSubsystem::QuerySceneMap(const FString& Sql)
{
	TArray<FStructSceneManage> SceneList;
	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{

		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			FStructSceneManage Scene;
			Scene.Name = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
			Scene.Time = UC_DataAccessBPLibrary::getValueAsString(1, queryId);
			Scene.LevelName = UC_DataAccessBPLibrary::getValueAsString(2, queryId);
			Scene.MapId = UC_DataAccessBPLibrary::getValueAsInt64(3, queryId);
			Scene.Type = UC_DataAccessBPLibrary::getValueAsInt(4, queryId);
			Scene.picture=UC_DataAccessBPLibrary::getValueAsByteArray(5,queryId);
			SceneList.Add(Scene);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	return SceneList;
}

bool UC_DecisionSuggestSubsystem::CheckMapNameisExits(const FString& mapName)
{
	//检查是否有重名的地图名称
	FString Name;
	FString sqlInsert = FString::Printf(TEXT("SELECT name FROM map WHERE name = '%s';"), *mapName);
	int quryid;
	if (UC_DataAccessBPLibrary::execQuery(sqlInsert, quryid))
	{
		while (UC_DataAccessBPLibrary::nextRecord(quryid))
		{
			Name = UC_DataAccessBPLibrary::getValueAsString(0, quryid);
		}
		
		UC_DataAccessBPLibrary::closeQuery(quryid);
	}
	
	return Name.Equals(mapName, ESearchCase::CaseSensitive);
}

bool UC_DecisionSuggestSubsystem::InsertMapSql(const int64& id, const FString& mapName, const FString& createTime, const FString& mapLevelName, const FString& modifyTime, const int32& built_in, const int32& type)
{
	// 构建插入数据的 SQL 查询语句
	FString sqlInsert = FString::Printf(TEXT("INSERT INTO map(id, name, create_time, level_name, modify_time, built_in,type) VALUES ("));
	sqlInsert += FString::Printf(TEXT("%lld, '%s',  '%s',  '%s', '%s',%d, %d);"), id, *mapName, *createTime,  *mapLevelName, *modifyTime, built_in,type);
	int quryid;
	bool temp = false;
	if (UC_DataAccessBPLibrary::prepare(sqlInsert, quryid))
	{
		temp = UC_DataAccessBPLibrary::execPrepare(quryid);
	}
	UC_DataAccessBPLibrary::closeQuery(quryid);
	UE_LOG(LogTemp, Warning, TEXT("InsertSceneSql:  %s"), *sqlInsert);
	return temp;

}

bool UC_DecisionSuggestSubsystem::UpdateSceneMapData(const int64 Id,const FString& NewName, const FString& ModifyTime)
{
	FString sql = FString::Printf(TEXT("UPDATE map SET name = ?, modify_time = ? WHERE id=%lld"),Id);

	int queryId;
	UC_DataAccessBPLibrary::prepare(sql, queryId);//预备sql语句
	UC_DataAccessBPLibrary::bindValueString(0, NewName, queryId);
	UC_DataAccessBPLibrary::bindValueString(1, ModifyTime, queryId);

	UE_LOG(LogTemp, Warning, TEXT("UpdateSceneSql:  %s"), *sql);
	return UC_DataAccessBPLibrary::execPrepare(queryId);
}

bool UC_DecisionSuggestSubsystem::DeleteSceneMapData(const int64& id)
{

	FString sql = FString::Printf(TEXT("DELETE FROM scenario_main WHERE map_id=%lld"), id);
	
	UC_DataAccessBPLibrary::execDelete(sql);
	
	sql = FString::Printf(TEXT("DELETE FROM map WHERE id=%lld"), id);

	return UC_DataAccessBPLibrary::execDelete(sql);
}

FStructSceneManage UC_DecisionSuggestSubsystem::GetSceneEntityDataById(const int64& id)
{
	FStructSceneManage SceneData;
	FString Sql = FString::Printf(TEXT(
		"SELECT name "
		"FROM model "
		"WHERE id='%lld' "),id);
	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{

		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			SceneData.Name = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	return SceneData;
}

#pragma endregion


/*-----------------------训练管理--------------------------*/
#pragma region TrainManage

TArray<FStructTrainManage> UC_DecisionSuggestSubsystem::QueryTrainMapData()
{
	TArray<FStructTrainManage> TrainList;

	FString Sql = FString::Printf(TEXT(
		"SELECT scenario_main.name,scenario_main.map_id,scenario_main.create_time,scenario_main.id,map.level_name,map.name,scenario_main.type,scenario_main.snapshoot,scenario_main.introduce "
		"FROM \"scenario_main\" "
		"JOIN map ON scenario_main.map_id = map.id "
		"ORDER BY scenario_main.create_time DESC"));

	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{

		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			FStructTrainManage Train;
			Train.Name = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
			Train.MapId = UC_DataAccessBPLibrary::getValueAsInt64(1, queryId);
			Train.Time = UC_DataAccessBPLibrary::getValueAsString(2, queryId);
			Train.ScenarioId = UC_DataAccessBPLibrary::getValueAsInt64(3, queryId);
			Train.SceneData.LevelName = UC_DataAccessBPLibrary::getValueAsString(4, queryId);
			Train.SceneData.Name= UC_DataAccessBPLibrary::getValueAsString(5, queryId);
			Train.Type = UC_DataAccessBPLibrary::getValueAsInt(6, queryId);
			Train.picture=UC_DataAccessBPLibrary::getValueAsByteArray(7,queryId);
			Train.Introduce= UC_DataAccessBPLibrary::getValueAsString(8, queryId);
			TrainList.Add(Train);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	return TrainList;
}

TArray<FStructTrainManage> UC_DecisionSuggestSubsystem::QueryTrainDataByFilter(const FString& SceneName, const FString& SceneType, const FString& StartTime, const FString& EndTime)
{
	//FString Type = TEXT("全部");

	//FString TrainPath = GetTrainPath(SceneType);

	FString Sql = FString::Printf(TEXT(
		"SELECT scenario_main.name,scenario_main.map_id,scenario_main.create_time,scenario_main.id,map.level_name,map.name,scenario_main.type,scenario_main.snapshoot,scenario_main.introduce "
		"FROM \"scenario_main\" "
		"JOIN map ON scenario_main.map_id = map.id "
		"WHERE 1=1 "));
		
	FString EndSql = TEXT("ORDER BY scenario_main.create_time DESC");

	if (SceneName.IsEmpty() && SceneType == NoType)
	{
		Sql += FString::Printf(TEXT(" AND scenario_main.create_time BETWEEN '%s' AND '%s' "), *StartTime, *EndTime);
		Sql += EndSql;
		UE_LOG(LogTemp, Warning, TEXT("TrainSql:  %s"), *Sql);
		return QueryTrainMap(Sql);
	}

	if (!SceneName.IsEmpty())
	{
		Sql += FString::Printf(TEXT("AND scenario_main.name like '%%%s%%' "), *SceneName);
		
	}
	if (SceneType != NoType)
	{
		Sql += FString::Printf(TEXT(" AND scenario_main.type = '%s' "), *SceneType);
	}

	Sql += FString::Printf(TEXT(" AND scenario_main.create_time BETWEEN '%s' AND '%s' "), *StartTime, *EndTime);

	Sql += EndSql;

	UE_LOG(LogTemp, Warning, TEXT("TrainSql:  %s"), *Sql);
	return QueryTrainMap(Sql);
}

TArray<FStructTrainManage> UC_DecisionSuggestSubsystem::QueryTrainMap(const FString& Sql)
{
	TArray<FStructTrainManage> TrainList;

	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{
		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			FStructTrainManage Train;
			Train.Name = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
			Train.MapId = UC_DataAccessBPLibrary::getValueAsInt64(1, queryId);
			Train.Time = UC_DataAccessBPLibrary::getValueAsString(2, queryId);
			Train.ScenarioId = UC_DataAccessBPLibrary::getValueAsInt64(3, queryId);
			Train.SceneData.LevelName = UC_DataAccessBPLibrary::getValueAsString(4, queryId);
			Train.SceneData.Name = UC_DataAccessBPLibrary::getValueAsString(5, queryId);
			Train.Type = UC_DataAccessBPLibrary::getValueAsInt(6, queryId);
			Train.picture=UC_DataAccessBPLibrary::getValueAsByteArray(7,queryId);
			Train.Introduce= UC_DataAccessBPLibrary::getValueAsString(8, queryId);
			TrainList.Add(Train);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	return TrainList;
}

bool UC_DecisionSuggestSubsystem::CheckTrainNameisExits(const FString& mapName)
{
	//检查是否有重名的地图名称
	FString Name;
	FString sqlInsert = FString::Printf(TEXT("SELECT name FROM scenario_main WHERE name = '%s';"), *mapName);
	int quryid;

	if (UC_DataAccessBPLibrary::execQuery(sqlInsert, quryid))
	{
		while (UC_DataAccessBPLibrary::nextRecord(quryid))
		{
			Name = UC_DataAccessBPLibrary::getValueAsString(0, quryid);
		}

		UC_DataAccessBPLibrary::closeQuery(quryid);
	}

	return Name.Equals(mapName, ESearchCase::CaseSensitive);
}

bool UC_DecisionSuggestSubsystem::InsertTrainMapSql(const int64& id, const FString& TrainName, const int64& MapId, const FString& createTime, const int32& type,const FString& Author,const FString& Introduce)
{
	// 构建插入数据的 SQL 查询语句
	FString sqlInsert = FString::Printf(TEXT("INSERT INTO scenario_main(id, name, map_id, create_time,type,author,introduce) VALUES ("));
	sqlInsert += FString::Printf(TEXT("%lld, '%s', %lld, '%s',%d,'%s','%s');"), id, *TrainName, MapId, *createTime, type,*Author,*Introduce);
	int quryid;
	bool temp = false;
	if (UC_DataAccessBPLibrary::prepare(sqlInsert, quryid))
	{
		temp = UC_DataAccessBPLibrary::execPrepare(quryid);
	}
	UC_DataAccessBPLibrary::closeQuery(quryid);
	UE_LOG(LogTemp, Warning, TEXT("InsertTrainSql:  %s"), *sqlInsert);
	return temp;
}

bool UC_DecisionSuggestSubsystem::UpdateTrainMapData(const int64 Id, const FString& NewName, const FString& ModifyTime,const FString& Author, const FString& Introduce)
{
	FString sql = FString::Printf(TEXT("UPDATE scenario_main SET name = ?, modify_time = ?,author = ?, introduce = ? WHERE id=%lld"), Id);

	int queryId;
	UC_DataAccessBPLibrary::prepare(sql, queryId);//预备sql语句
	UC_DataAccessBPLibrary::bindValueString(0, NewName, queryId);
	UC_DataAccessBPLibrary::bindValueString(1, ModifyTime, queryId);
	UC_DataAccessBPLibrary::bindValueString(2, Author, queryId);
	UC_DataAccessBPLibrary::bindValueString(3, Introduce, queryId);

	UE_LOG(LogTemp, Warning, TEXT("UpdateTrainSql:  %s"), *sql);
	return UC_DataAccessBPLibrary::execPrepare(queryId);
}

bool UC_DecisionSuggestSubsystem::DeleteTrainMapData(const int64& id)
{
	FString sql = FString::Printf(TEXT("DELETE FROM scenario_main WHERE id=%lld"), id);
	return UC_DataAccessBPLibrary::execDelete(sql);
}

FStructTrainManage UC_DecisionSuggestSubsystem::GetTrainDataById(const int64& id)
{
	//检查是否有重名的地图名称
	FStructTrainManage TrainData;
	FString sqlInsert = FString::Printf(TEXT("SELECT * FROM scenario_main WHERE id = '%lld';"), id);
	int quryid;

	if (UC_DataAccessBPLibrary::execQuery(sqlInsert, quryid))
	{
		while (UC_DataAccessBPLibrary::nextRecord(quryid))
		{
			TrainData.ScenarioId = UC_DataAccessBPLibrary::getValueAsInt64(0, quryid);
			TrainData.Name = UC_DataAccessBPLibrary::getValueAsString(1, quryid);
			TrainData.MapId = UC_DataAccessBPLibrary::getValueAsInt64(2, quryid);
			TrainData.Author = UC_DataAccessBPLibrary::getValueAsString(3, quryid);
			TrainData.Type = UC_DataAccessBPLibrary::getValueAsInt(4, quryid);
			TrainData.Introduce = UC_DataAccessBPLibrary::getValueAsString(6, quryid);
			TrainData.Time = UC_DataAccessBPLibrary::getValueAsString(8, quryid);
			TrainData.picture=UC_DataAccessBPLibrary::getValueAsByteArray(16,quryid);
		}

		UC_DataAccessBPLibrary::closeQuery(quryid);
	}

	return TrainData;
}

bool UC_DecisionSuggestSubsystem::UpdateTrainToDB(const FStructTrainManage& Tarin)
{
	FString Sql = FString("UPDATE scenario_main SET all_scenario=(?),red_scenario=(?),blue_scenaio=(?) WHERE id = ") + UC_DataAccessBPLibrary::convertInt64ToFString(Tarin.ScenarioId);

	FString Dir = FPaths::ProjectSavedDir() + "SaveGames/" + UC_DataAccessBPLibrary::convertInt64ToFString(Tarin.ScenarioId) + "/";
	IPlatformFile& F = FPlatformFileManager::Get().GetPlatformFile();
	Dir = FPaths::ConvertRelativePathToFull(Dir);
	if (!FPaths::DirectoryExists(Dir)) return false;

	int queryID;
	UC_DataAccessBPLibrary::prepare(Sql, queryID);
	
	//存档更新
	FString tempPath = Dir + "Neutrality";
	TArray<uint8> tValue = UC_DataAccessBPLibrary::CompressDirToCharData(tempPath);
	UC_DataAccessBPLibrary::bindValueByteArray(0, tValue, queryID);
	tempPath = Dir + "Red";
	tValue = UC_DataAccessBPLibrary::CompressDirToCharData(tempPath);
	UC_DataAccessBPLibrary::bindValueByteArray(1, tValue, queryID);
	tempPath = Dir + "Blue";
	tValue = UC_DataAccessBPLibrary::CompressDirToCharData(tempPath);
	UC_DataAccessBPLibrary::bindValueByteArray(2, tValue, queryID);

	return UC_DataAccessBPLibrary::execPrepare(queryID);
}

#pragma endregion


/*-----------------------导调控制--------------------------*/
#pragma region SimControl

TArray<FStructSimControl> UC_DecisionSuggestSubsystem::QuerySimMapData()
{
	TArray<FStructSimControl> SimList;

	FString Sql = FString::Printf(TEXT(
		"SELECT map.level_name,scenario_main.id,sim_main.id,sim_main.name,sim_main.start_time,map.id,scenario_main.type "
		"FROM \"sim_main\" "
		"JOIN scenario_main ON sim_main.scenario_id = scenario_main.id "
		"JOIN map ON scenario_main.map_id = map.id "
		"ORDER BY sim_main.start_time DESC"));

	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{
		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			FStructSimControl Sim;
			Sim.TrainData.SceneData.LevelName = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
			Sim.ScenarioId = UC_DataAccessBPLibrary::getValueAsInt64(1, queryId);;
			Sim.SimId = UC_DataAccessBPLibrary::getValueAsInt64(2, queryId);
			Sim.Name = UC_DataAccessBPLibrary::getValueAsString(3, queryId);
			Sim.Time = UC_DataAccessBPLibrary::getValueAsString(4, queryId);
			Sim.MapId = UC_DataAccessBPLibrary::getValueAsInt64(5, queryId);
			Sim.TrainData.Type = UC_DataAccessBPLibrary::getValueAsInt(6, queryId);
			SimList.Add(Sim);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	return SimList;
}

TArray<FStructSimControl> UC_DecisionSuggestSubsystem::QuerySimDataByFilter(const FString& SceneName, const FString& SceneType, const FString& StartTime, const FString& EndTime)
{
	FString Type = TEXT("全部");

	FString TrainPath = GetTrainPath(SceneType);

	FString Sql = FString::Printf(TEXT(
		"SELECT map.level_name,scenario_main.id,sim_main.id,sim_main.name,sim_main.start_time "
		"FROM \"sim_main\" "
		"JOIN scenario_main ON sim_main.scenario_id = scenario_main.id "
		"JOIN map ON scenario_main.map_id = map.id "
		"WHERE 1=1 "));

	FString EndSql = TEXT("ORDER BY sim_main.start_time DESC");

	if (SceneName.IsEmpty() && TrainPath == Type)
	{
		Sql += FString::Printf(TEXT(" AND sim_main.start_time BETWEEN '%s' AND '%s' "), *StartTime, *EndTime);
		Sql += EndSql;
		UE_LOG(LogTemp, Warning, TEXT("SimSql:  %s"), *Sql);
		return QuerySimMap(Sql);
	}

	if (!SceneName.IsEmpty())
	{
		Sql += FString::Printf(TEXT("AND sim_main.name='%s' "), *SceneName);

	}
	if (TrainPath != Type)
	{
		Sql += FString::Printf(TEXT(" AND map.level_name = '%s' "), *TrainPath);
	}

	Sql += FString::Printf(TEXT(" AND sim_main.start_time BETWEEN '%s' AND '%s' "), *StartTime, *EndTime);

	Sql += EndSql;

	UE_LOG(LogTemp, Warning, TEXT("SimSql:  %s"), *Sql);
	return QuerySimMap(Sql);
}



TArray<FStructSimControl> UC_DecisionSuggestSubsystem::QuerySimMap(const FString& Sql)
{
	TArray<FStructSimControl> SimList;

	int32 queryId;
	if (UC_DataAccessBPLibrary::execQuery(Sql, queryId))
	{
		while (UC_DataAccessBPLibrary::nextRecord(queryId))
		{
			FStructSimControl Sim;
			Sim.TrainData.SceneData.LevelName = UC_DataAccessBPLibrary::getValueAsString(0, queryId);
			Sim.ScenarioId = UC_DataAccessBPLibrary::getValueAsInt64(1, queryId);;
			Sim.SimId = UC_DataAccessBPLibrary::getValueAsInt64(2, queryId);
			Sim.Name = UC_DataAccessBPLibrary::getValueAsString(3, queryId);
			Sim.Time = UC_DataAccessBPLibrary::getValueAsString(4, queryId);
			SimList.Add(Sim);
		}
		UC_DataAccessBPLibrary::closeQuery(queryId);
	}
	return SimList;
}

bool UC_DecisionSuggestSubsystem::JudgeIsPatrolByMapPath(const FString& Path)
{
	const FString Filter = GetFilterPath()[0];
	bool IsPatrol = Path.Contains(Filter);
	return IsPatrol;
}

bool UC_DecisionSuggestSubsystem::JudgeIsPatrolByMapName(const FString& Path)
{
	const FString Filter = GetMapName()[0];
	bool IsPatrol = Path.Contains(Filter);
	return IsPatrol;
}

TArray<FString> UC_DecisionSuggestSubsystem::GetFilterPath()
{
	TArray<FString> FilterPath;
	FString FilterPathA = TEXT("/P_SPSMap/BigCompanyArchViz/Maps/1111111Map_TestMap_UI");
	FString FilterPathB = TEXT("/P_SPSMap/BigCompanyArchViz/Maps/Test/Map_Scene");
	FilterPath.Add(FilterPathA);
	FilterPath.Add(FilterPathB);
	return FilterPath;
}
TArray<FString> UC_DecisionSuggestSubsystem::GetMapName()
{
	TArray<FString> FilterPath;
	FString FilterPathA = TEXT("1111111Map_TestMap_UI");
	FString FilterPathB = TEXT("Map_Scene");
	FilterPath.Add(FilterPathA);
	FilterPath.Add(FilterPathB);
	return FilterPath;
}
#pragma endregion