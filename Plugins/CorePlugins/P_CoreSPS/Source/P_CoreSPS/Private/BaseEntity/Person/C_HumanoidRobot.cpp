// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利


#include "BaseEntity/Person/C_HumanoidRobot.h"

#include "C_HDF5Util.h"
#include "Component/EntityComponent/Weapon/C_LaserWeaponComponent.h"
#include "GamePlay/C_GameInstanceSPS.h"
#include "Kismet/GameplayStatics.h"


AC_HumanoidRobot::AC_HumanoidRobot(const FObjectInitializer& ObjectInitializer): Super(ObjectInitializer)
{
	
	PrimaryActorTick.bCanEverTick = true;
	RobotID = -1;
}

void AC_HumanoidRobot::OnRecordJointState()
{
	// UC_HDF5Util::WriteJointDataToHDF5();
	// Mesh->GetBoneIndex()
}

void AC_HumanoidRobot::BeginPlay()
{
	Super::BeginPlay();
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(this);
	if (GameInstance == nullptr)
		return;

	UC_GameInstanceSPS* GameInstanceSPS = Cast<UC_GameInstanceSPS>(GameInstance);
	if (GameInstanceSPS == nullptr || GameInstanceSPS->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
		return;

	GetWorld()->GetTimerManager().SetTimer(TimerHandle, this, &AC_HumanoidRobot::OnRecordJointState, 0.5f, true);
}

void AC_HumanoidRobot::SetRobotID(int InValue)
{
	RobotID = InValue;
}

bool AC_HumanoidRobot::ContainWeapon(int WeaponID)
{
	for(auto Elem: DeviceRelationships)
	{
		TWeakObjectPtr<UC_BaseDeviceComponent> DeviceComponent = *DeviceInfos.Find(Elem.Value.MainDeviceVersion);
		if (!DeviceComponent.IsValid())
			continue;

		UC_LaserWeaponComponent* LaserWeaponComponent = Cast<UC_LaserWeaponComponent>(DeviceComponent);
		if (LaserWeaponComponent == nullptr)
			continue;

		if (LaserWeaponComponent->GetDeviceID() == WeaponID)
			return true;
	}

	return false;
}

float AC_HumanoidRobot::PlayAnimMontage(class UAnimMontage* AnimMontage, float InPlayRate, FName StartSectionName)
{
	UAnimInstance * AnimInstance = (GetAnimationMesh())? GetAnimationMesh()->GetAnimInstance() : nullptr; 
	if( AnimMontage && AnimInstance )
	{
		float const Duration = AnimInstance->Montage_Play(AnimMontage, InPlayRate);

		if (Duration > 0.f)
		{
			// Start at a given Section.
			if( StartSectionName != NAME_None )
			{
				AnimInstance->Montage_JumpToSection(StartSectionName, AnimMontage);
			}

			return Duration;
		}
	}	

	return 0.f;
}
#pragma region EntityConfigurationInterface

TArray<TScriptInterface<IC_ConfigurableObjectInterface>> AC_HumanoidRobot::GetAllConfigurableComponent_Implementation()
{
	TArray<TScriptInterface<IC_ConfigurableObjectInterface>> ConfigurableObjectInterfaces;
	TArray<UActorComponent*> Components;
	GetComponents<UActorComponent>(Components);
	
	for (UActorComponent* Component : Components)
	{
		if (IC_ConfigurableObjectInterface* ConfigurableObjectInterface = Cast<IC_ConfigurableObjectInterface>(Component))
		{
			TScriptInterface<IC_ConfigurableObjectInterface> TmpScriptInterface;
			TmpScriptInterface.SetInterface(ConfigurableObjectInterface);
			TmpScriptInterface.SetObject(Component);
			ConfigurableObjectInterfaces.Add(TmpScriptInterface);
		}
	}

	
	TScriptInterface<IC_ConfigurableObjectInterface> TmpScriptInterface;
	TmpScriptInterface.SetInterface(this);
	TmpScriptInterface.SetObject(this);
	ConfigurableObjectInterfaces.Add(TmpScriptInterface);

	return ConfigurableObjectInterfaces;
}

FString AC_HumanoidRobot::GetConfigurableObjectName_Implementation() const
{
	return TEXT("机器人");
}

TArray<FConfigurationItemInfo> AC_HumanoidRobot::GetConfigurationItemInfos_Implementation() const
{
	TArray<FConfigurationItemInfo> ConfigurationItemInfos;
	FConfigurationItemInfo ConfigurationItemInfo;
	ConfigurationItemInfo.Name = TEXT("编号");
	ConfigurationItemInfo.Type = EConfigurationItemType::Int;
	ConfigurationItemInfo.CurValue = FString::Printf(TEXT("%d"), RobotID);

	ConfigurationItemInfos.Add(ConfigurationItemInfo);
	return ConfigurationItemInfos;
}

bool AC_HumanoidRobot::SetIntItem_Implementation(const FString& ItemName, const int& Value)
{
	if (ItemName != TEXT("编号"))
		return false;

	RobotID = Value;
	return true;
}

bool AC_HumanoidRobot::SetFloatItem_Implementation(const FString& ItemName, const float& Value)
{
	// 没有浮点数的配置项
	return false;
}

bool AC_HumanoidRobot::SetStringItem_Implementation(const FString& ItemName, const FString& Value)
{
	// 没有字符串的配置项
	return false;
}

#pragma endregion 