// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利


#include "Component/EntityComponent/C_ROSDriverComponent.h"

#include "C_HDF5Util.h"
#include "XmlFile.h"
#include "BaseEntity/Person/C_BaseCharacter.h"
#include "GamePlay/C_GameInstanceSPS.h"
#include "GamePlay/C_GameMode.h"
#include "Interfaces/IPluginManager.h"
#include "Kismet/GameplayStatics.h"
#include "PreOpenCVHeaders.h"
#include "opencv2/imgcodecs.hpp"
#include "opencv2/core/mat.hpp"
#include "OpenCVHelper.h"
#include "opencv2/imgproc.hpp"
#include "PostOpenCVHeaders.h"
#include "Rendering/SkinWeightVertexBuffer.h"
#include "ROSMsg/GeometryMsgs/C_PoseMsg.h"
#include "ROSMsg/GeometryMsgs/C_PoseWithCovarianceMsg.h"
#include "ROSMsg/GeometryMsgs/C_TwistMsg.h"
#include "ROSMsg/GeometryMsgs/C_TwistWithCovarianceMsg.h"
#include "ROSMsg/NavMsgs/C_OdometryMsg.h"
#include "ROSMsg/SensorMsgs/C_CompressedImageMsg.h"
#include "ROSMsg/SensorMsgs/C_ImageMsg.h"
#include "ROSMsg/SensorMsgs/C_ImuMsg.h"
#include "ROSMsg/SensorMsgs/C_JointStateMsg.h"
#include "Transport/C_Topic.h"

TArray<FString> UC_ROSDriverComponent::ImageConfigurationNames = {
	TEXT("D435i左目主题"),
	TEXT("D435i右目主题"),
	TEXT("D435i深度主题"),
	TEXT("Zed左目主题"),
	TEXT("Zed右目主题"),
};


UC_ROSDriverComponent::UC_ROSDriverComponent()
{
	PrimaryComponentTick.bCanEverTick = false;

	// 默认值
	OdometryTopicName = TEXT("/odometry");
	JointStatesTopicName = TEXT("/joint_states");
	for (const FString& ImageConfigurationName: ImageConfigurationNames)
	{
		ImageTopicConfigurationItemValues.Add(ImageConfigurationName, TEXT("/camera"));
	}
	
	IMUTopicConfigurationItemValues.Add(TEXT("IMU主题"),TEXT("/imu"));
}

bool UC_ROSDriverComponent::GetSkeletonState(FSkeletonState& SkeletonState)
{
	if (!TESPGameMode.IsValid())
		return false;
	
	// 当仿真未开始时，不返回数据
	if (!TESPGameMode->HasSimulationStarted() || Owner == nullptr)
		return false;
	
	return SkeletonStateQueue.Dequeue(SkeletonState);
}

TArray<FString> UC_ROSDriverComponent::GetImageItemNames() const
{
	return ImageConfigurationNames;
}

const TMap<FName, bool>& UC_ROSDriverComponent::GetJointStateModifier() const
{
	return JointStateNegative;
}

UTexture2D* UC_ROSDriverComponent::GetImageTexture(const FString& ItemName) const
{
	if (!ImageTextures.Contains(ItemName))
		return nullptr;

	return ImageTextures[ItemName];
}

void UC_ROSDriverComponent::GetIMUData(FQuat& OrientationLoc, FVector& AngularVelocityLoc, FVector& LinearAccelerationLoc)
{
	OrientationLoc=Orientation;
	AngularVelocityLoc=AngularVelocity;
	LinearAccelerationLoc=LinearAcceleration;
}

#pragma region ConfigurableObjectInterface

FString UC_ROSDriverComponent::GetConfigurableObjectName_Implementation() const
{
	return TEXT("ROS数据驱动器");
}

TArray<FConfigurationItemInfo> UC_ROSDriverComponent::GetConfigurationItemInfos_Implementation() const
{
	TArray<FConfigurationItemInfo> ConfigurationItemInfos;

	if (!TESPGameMode.IsValid())
		return ConfigurationItemInfos;
	
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(this);
	if (GameInstance == nullptr)
		return ConfigurationItemInfos;

	UC_GameInstanceSPS* GameInstanceSps = Cast<UC_GameInstanceSPS>(GameInstance);
	if (GameInstanceSps == nullptr)
		return ConfigurationItemInfos;

	// 不是监控模式而且在导调不显示配置项
	if (GameInstanceSps->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring && TESPGameMode->IsPlayGame())
		return ConfigurationItemInfos;

	FConfigurationItemInfo OdometryConfigurationItemInfo;
	OdometryConfigurationItemInfo.Name = TEXT("里程计主题");
	OdometryConfigurationItemInfo.Type = EConfigurationItemType::String;
	OdometryConfigurationItemInfo.CurValue = OdometryTopicName;
	ConfigurationItemInfos.Add(OdometryConfigurationItemInfo);

	FConfigurationItemInfo PoseConfigurationItemInfo;
	PoseConfigurationItemInfo.Name = TEXT("关节状态主题");
	PoseConfigurationItemInfo.Type = EConfigurationItemType::String;
	PoseConfigurationItemInfo.CurValue = JointStatesTopicName;
	ConfigurationItemInfos.Add(PoseConfigurationItemInfo);
	
	for (const auto& Pair: ImageTopicConfigurationItemValues)
	{
		FConfigurationItemInfo ImageConfigurationItemInfo;
		ImageConfigurationItemInfo.Name = Pair.Key;
		ImageConfigurationItemInfo.Type = EConfigurationItemType::String;
		ImageConfigurationItemInfo.CurValue = Pair.Value;
		ConfigurationItemInfos.Add(ImageConfigurationItemInfo);
	}
	
	for(const auto& Pair: IMUTopicConfigurationItemValues)
	{
		FConfigurationItemInfo ImageConfigurationItemInfo;
		ImageConfigurationItemInfo.Name = Pair.Key;
		ImageConfigurationItemInfo.Type = EConfigurationItemType::String;
		ImageConfigurationItemInfo.CurValue = Pair.Value;
		ConfigurationItemInfos.Add(ImageConfigurationItemInfo);
	}

	for (const auto& Pair: JointStateNegative)
	{
		FConfigurationItemInfo SkeletonConfigurationItemInfo;
		SkeletonConfigurationItemInfo.Name = Pair.Key.ToString();
		SkeletonConfigurationItemInfo.Type = EConfigurationItemType::Float;
		SkeletonConfigurationItemInfo.CurValue = FString::Printf(TEXT("%f"), Pair.Value ? -1.f : 1.f);
		ConfigurationItemInfos.Add(SkeletonConfigurationItemInfo);
	}
	return ConfigurationItemInfos;
}

bool UC_ROSDriverComponent::SetFloatItem_Implementation(const FString& ItemName, const float& Value)
{
	if (!JointStateNegative.Contains(FName(ItemName)))
		return false;

	JointStateNegative[FName(ItemName)] = (Value == -1.f);
	return true;
}

bool UC_ROSDriverComponent::SetStringItem_Implementation(const FString& ItemName, const FString& Value)
{
	if (ItemName == TEXT("里程计主题"))
	{
		if(Value.Equals(OdometryTopicName, ESearchCase::CaseSensitive))
			return true;
		
		OdometryTopicName = Value;
		// 非监控模式下，不订阅消息
		if (!SPSGameInstance.IsValid() || SPSGameInstance->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
			return true;
		OdometryTopic->ChangeTopicName(OdometryTopicName);
	}
	else if (ItemName ==  TEXT("关节状态主题"))
	{
		if(Value.Equals(JointStatesTopicName, ESearchCase::CaseSensitive))
			return true;
		
		JointStatesTopicName = Value;
		// 非监控模式下，不订阅消息
		if (!SPSGameInstance.IsValid() || SPSGameInstance->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
			return true;
		JointStatesTopic->ChangeTopicName(JointStatesTopicName);
	}
	else if (ItemName == TEXT("D435i左目主题") || ItemName == TEXT("D435i右目主题") || ItemName == TEXT("D435i深度主题") || ItemName == TEXT("Zed左目主题") || ItemName == TEXT("Zed右目主题"))
	{
		if (!ImageTopicConfigurationItemValues.Contains(ItemName))
			ImageTopicConfigurationItemValues.Add(ItemName, TEXT("/camera"));
		
		/*if (Value == ImageTopicConfigurationItemValues[ItemName])
			return true;*/

		if(Value.Equals(ImageTopicConfigurationItemValues[ItemName], ESearchCase::CaseSensitive))
			return true;
		
		ImageTopicConfigurationItemValues[ItemName] = Value;
		// 非监控模式下，不订阅消息
		if (!SPSGameInstance.IsValid() || SPSGameInstance->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
			return true;
		
		if (!ImageTopics.Contains(ItemName))
		{
			UC_Topic* ImageTopic = NewObject<UC_Topic>(this);
			// ImageTopic->Init(ItemName, TEXT("sensor_msgs/Image"));
			// FROSMessageDelegate ImageMsgDelegate;
			// ImageMsgDelegate.BindDynamic(this, &ThisClass::OnImageMsgReceived);
			ImageTopic->Init(ItemName, TEXT("sensor_msgs/CompressedImage"));
			FROSMessageDelegate ImageMsgDelegate;
			ImageMsgDelegate.BindDynamic(this, &ThisClass::OnCompressImageMsgReceived);
			ImageTopic->Subscribe(ImageMsgDelegate);

			ImageTopics.Add(ItemName, ImageTopic);
		}
		else
		{
			ImageTopics[ItemName]->ChangeTopicName(Value);
		}
	}
	else if(ItemName == TEXT("IMU主题"))
	{
		if (!IMUTopicConfigurationItemValues.Contains(ItemName))
			IMUTopicConfigurationItemValues.Add(ItemName, TEXT("/imu"));
		
		/*if (Value == IMUTopicConfigurationItemValues[ItemName])
			return true;*/

		if(Value.Equals(IMUTopicConfigurationItemValues[ItemName], ESearchCase::CaseSensitive))
			return true;
		
		IMUTopicConfigurationItemValues[ItemName] = Value;

		// 监控模式下，采取订阅消息
		if (!SPSGameInstance.IsValid() || SPSGameInstance->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
			return true;
		
		if (!IMUTopics.Contains(ItemName))
		{
			UC_Topic* IMUTopic = NewObject<UC_Topic>(this);
			IMUTopic->Init(ItemName, TEXT("sensor_msgs/Imu"));
			FROSMessageDelegate IMUMsgDelegate;
			IMUMsgDelegate.BindDynamic(this, &ThisClass::OnIMUMsgReceived);
			IMUTopic->Subscribe(IMUMsgDelegate);

			IMUTopics.Add(ItemName, IMUTopic);
		}
		else
		{
			IMUTopics[ItemName]->ChangeTopicName(Value);
		}
	}
	else
	{
		return false;
	}
	
	return true;
}

bool UC_ROSDriverComponent::SetIntItem_Implementation(const FString& ItemName, const int& Value)
{
	return false;
}

#pragma endregion

#pragma region EMSCompSaveInterface

void UC_ROSDriverComponent::ComponentLoaded_Implementation()
{
	// 监控模式下，订阅相机图像
	if (!SPSGameInstance.IsValid() || SPSGameInstance->GetBusinessUsageModel() != EBusinessUsageModel::Monitoring)
		return;

	for (const auto& Pair: ImageTopicConfigurationItemValues)
	{
		UC_Topic* ImageTopic = NewObject<UC_Topic>(this);
		// ImageTopic->Init(Pair.Value, TEXT("sensor_msgs/Image"));
		// FROSMessageDelegate ImageMsgDelegate;
		// ImageMsgDelegate.BindDynamic(this, &ThisClass::OnImageMsgReceived);
		ImageTopic->Init(Pair.Value, TEXT("sensor_msgs/CompressedImage"));
		FROSMessageDelegate ImageMsgDelegate;
		ImageMsgDelegate.BindDynamic(this, &ThisClass::OnCompressImageMsgReceived);
		ImageTopic->Subscribe(ImageMsgDelegate);

		ImageTopics.Add(Pair.Key, ImageTopic);
	}

	for (const auto& Pair: IMUTopicConfigurationItemValues)
	{
		UC_Topic* IMUTopic = NewObject<UC_Topic>(this);
		IMUTopic->Init(Pair.Value, TEXT("sensor_msgs/Imu"));
		FROSMessageDelegate IMUMsgDelegate;
		IMUMsgDelegate.BindDynamic(this, &ThisClass::OnIMUMsgReceived);
		IMUTopic->Subscribe(IMUMsgDelegate);

		IMUTopics.Add(Pair.Key, IMUTopic);
	}

	OdometryTopic = NewObject<UC_Topic>(this);
	OdometryTopic->Init(OdometryTopicName, TEXT("nav_msgs/Odometry"));
	FROSMessageDelegate OdometryMsgDelegate;
	OdometryMsgDelegate.BindDynamic(this, &ThisClass::OnOdometryMsgReceived);
	OdometryTopic->Subscribe(OdometryMsgDelegate);
	
	JointStatesTopic = NewObject<UC_Topic>(this);
	JointStatesTopic->Init(JointStatesTopicName, TEXT("sensor_msgs/JointState"));
	FROSMessageDelegate JointStateMsgDelegate;
	JointStateMsgDelegate.BindDynamic(this, &ThisClass::OnJointStatesMsgReceived);
	JointStatesTopic->Subscribe(JointStateMsgDelegate);
}

void UC_ROSDriverComponent::ComponentPreSave_Implementation()
{
}

#pragma endregion

void UC_ROSDriverComponent::BeginPlay()
{
	Super::BeginPlay();

	if (!ParseURDF())
		return;
	
	AGameModeBase* GameModeBase = UGameplayStatics::GetGameMode(this);
	if (GameModeBase == nullptr)
		return;

	TESPGameMode = Cast<AC_GameMode>(GameModeBase);

	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(this);
	if (GameInstance == nullptr)
		return;

	SPSGameInstance = Cast<UC_GameInstanceSPS>(GameInstance);
	Owner = Cast<AC_BaseCharacter>(GetOwner());
	if (Owner == nullptr)
		return;

	// 获取骨骼名称
	const USkeletalMeshComponent* SkeletalMeshComponent = Owner->GetMesh();
	if (SkeletalMeshComponent == nullptr || SkeletalMeshComponent->GetSkeletalMeshAsset() == nullptr)
		return;

	USkeletalMesh* SkeletalMesh = SkeletalMeshComponent->GetSkeletalMeshAsset();
	USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
	if (Skeleton == nullptr)
		return;

	const FReferenceSkeleton& ReferenceSkeleton = Skeleton->GetReferenceSkeleton();
	int32 BoneCount = ReferenceSkeleton.GetNum();
	for (int32 Index = 0; Index < BoneCount; ++Index)
	{
		FName BoneName = ReferenceSkeleton.GetBoneName(Index);
		SkeletonJointNames.Add(BoneName);
	}

	for (const auto &SkeletonJointName: SkeletonJointNames)
	{
		if (!SkeletonJointName.ToString().StartsWith(("Link_")))
			continue;
		
		JointStateNegative.Add(SkeletonJointName, false);
	}
		
}

void UC_ROSDriverComponent::OnOdometryMsgReceived(const UC_BaseROSMsg* ROSMsg)
{
	const UC_OdometryMsg* OdometryMsg = Cast<UC_OdometryMsg>(ROSMsg);
	if (OdometryMsg == nullptr)
		return;
	
	if (!bHasReceivedOdometryMsg)
	{
		bHasReceivedOdometryMsg = true;
		FirstOdometryPosition.X = OdometryMsg->GetPose()->GetPose()->GetPosition().X * 100;
		FirstOdometryPosition.Y = OdometryMsg->GetPose()->GetPose()->GetPosition().Y * -100;
		FirstOdometryPosition.Z = OdometryMsg->GetPose()->GetPose()->GetPosition().Z * 100;

		FQuat Quat;
		Quat.X = OdometryMsg->GetPose()->GetPose()->GetOrientation().X;
		Quat.Y = -OdometryMsg->GetPose()->GetPose()->GetOrientation().Y;
		Quat.Z = OdometryMsg->GetPose()->GetPose()->GetOrientation().Z;
		Quat.W = -OdometryMsg->GetPose()->GetPose()->GetOrientation().W;
		FirstOdometryRotator = Quat.Rotator();
		FirstInverseOdometryRotator = FirstOdometryRotator.GetInverse();
	}
	
	// 当仿真未开始时，不处理
	if (!TESPGameMode->HasSimulationStarted() || Owner == nullptr)
		return;

	if (!bRobotOriginPositionInitialized)
	{
		bRobotOriginPositionInitialized = true;
		RobotOriginPosition = Owner->GetActorLocation();
		RobotOriginRotator = Owner->GetActorRotation();
	}
	
	FVector NewOdometryLocation;
	NewOdometryLocation.X = OdometryMsg->GetPose()->GetPose()->GetPosition().X * 100;
	NewOdometryLocation.Y = OdometryMsg->GetPose()->GetPose()->GetPosition().Y * -100;
	NewOdometryLocation.Z = OdometryMsg->GetPose()->GetPose()->GetPosition().Z * 100;
	UC_HDF5Util::WriteOdometryToHDF5(
		TEXT("odometry"),
		OdometryMsg->GetPose()->GetPose()->GetPosition(),
		OdometryMsg->GetPose()->GetPose()->GetOrientation(),
		OdometryMsg->GetTwist()->GetTwist()->GetLinear(),
		OdometryMsg->GetTwist()->GetTwist()->GetAngular()
		);

	FVector OdometryPositionOffset = NewOdometryLocation - FirstOdometryPosition;
	// 2. 计算基于当前朝向的偏移
	FVector LocalOffset = FirstInverseOdometryRotator.RotateVector(OdometryPositionOffset);
	FVector WorldOffset = RobotOriginRotator.RotateVector(LocalOffset);
	FVector NewRobotPosition = RobotOriginPosition + WorldOffset;
	NewRobotPosition.Z = RobotOriginPosition.Z;
	Owner->SetActorLocation(NewRobotPosition);

	FQuat NewOdometryQuat;
	NewOdometryQuat.X = OdometryMsg->GetPose()->GetPose()->GetOrientation().X;
	NewOdometryQuat.Y = -OdometryMsg->GetPose()->GetPose()->GetOrientation().Y;
	NewOdometryQuat.Z = OdometryMsg->GetPose()->GetPose()->GetOrientation().Z;
	NewOdometryQuat.W = -OdometryMsg->GetPose()->GetPose()->GetOrientation().W;

	FRotator OdometryRotationOffset = NewOdometryQuat.Rotator() - FirstOdometryRotator;
	FRotator NewRobotRotation = (RobotOriginRotator + OdometryRotationOffset).GetNormalized();
	Owner->SetActorRotation(NewRobotRotation);
}

void UC_ROSDriverComponent::OnJointStatesMsgReceived(const UC_BaseROSMsg* ROSMsg)
{
	FSkeletonState SkeletonState;
	const UC_JointStateMsg* JointStatesMsg = Cast<UC_JointStateMsg>(ROSMsg);
	if (JointStatesMsg == nullptr)
		return;
	
	TArray<FString> JointNames = JointStatesMsg->GetJointNames();
	TArray<double> Positions = JointStatesMsg->GetPositions();
	TArray<double> Velocities = JointStatesMsg->GetVelocities();
	UC_HDF5Util::WriteJointDataToHDF5(TEXT("JointStates"), Positions,  Velocities, JointNames);
	
	for (int Index = 0; Index < JointNames.Num(); ++Index)
	{
		const FString& ROSJointName = JointNames[Index];
		FString SkeletonName = "Link_" + ROSJointName.Mid(2);
		FName SkeletonJointName = FName(SkeletonName);
		FName URDFJointName = FName(ROSJointName);
		if (!SkeletonJointNames.Contains(SkeletonJointName) || !JointRotationAxes.Contains(URDFJointName))
			continue;

		FJointState JointState;
		const double RotationInDegree = FMath::RadiansToDegrees(Positions[Index]);
		switch(JointRotationAxes[URDFJointName])
		{
		case EAxis::X:
			JointState.Rotation.Pitch = RotationInDegree;
			break;

		case EAxis::Y:
			JointState.Rotation.Roll = -RotationInDegree;
			break;

		case EAxis::Z:
			JointState.Rotation.Yaw = RotationInDegree;
			break;

		default:
			UE_LOG(LogTemp, Error, TEXT("Unknown Joint Rotation Axis: %d"), JointRotationAxes[URDFJointName]);
			continue;
		}

		if (SkeletonName.Contains(TEXT("Link_arm_l")))
		{
			UE_LOG(LogTemp, Display, TEXT("%s: rotation in[%d] degree: %f, in arc: %f"), *SkeletonName, JointRotationAxes[URDFJointName],  RotationInDegree, Positions[Index])
		}
		SkeletonState.AddJointState(SkeletonJointName, JointState);
	}

	// 删除最老网格体状态
	if (SkeletonStateQueue.IsFull())
		SkeletonStateQueue.Dequeue();
	
	SkeletonStateQueue.Enqueue(SkeletonState);
}

bool UC_ROSDriverComponent::ParseURDF()
{
	const FString PluginBaseDir = FPaths::ConvertRelativePathToFull(IPluginManager::Get().FindPlugin("P_CoreSPS")->GetBaseDir());
	const FString URDFFilePath = FPaths::Combine(PluginBaseDir, TEXT("Config/AzureLoong.urdf"));
	// 加载 XML 文件
	FXmlFile XmlFile(URDFFilePath);
	if (!XmlFile.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("XML file is not valid: %s"), *XmlFile.GetLastError());
		return false;
	}

	// 获取 XML 根节点
	const FXmlNode* RootNode = XmlFile.GetRootNode();
	if (!RootNode)
	{
		UE_LOG(LogTemp, Error, TEXT("XML file does not have a root node"));
		return false;
	}

	// 遍历所有的子节点
	TArray<FXmlNode*> AllChildNodes = RootNode->GetChildrenNodes();
	for (const FXmlNode* ChildNode : AllChildNodes)
	{
		// 获取 joint 名称
		FString TagName = ChildNode->GetTag();
		if (TagName != TEXT("joint"))
			continue;

		// 获取 joint 名称
		FString NodeName = ChildNode->GetAttribute(TEXT("name"));

		// 获取 axis 节点
		const FXmlNode* AxisNode = ChildNode->FindChildNode(TEXT("axis"));
		if (AxisNode == nullptr)
			return false;
		
		// 获取 axis 的 xyz 属性
		FString AxisStr = AxisNode->GetAttribute(TEXT("xyz"));
		TArray<FString> AxisArr;
		AxisStr.ParseIntoArray(AxisArr, TEXT(" "));
		int Index = AxisArr.Contains(TEXT("1")) ? AxisArr.Find(TEXT("1")) : AxisArr.Find(TEXT("-1"));
		if (Index == INDEX_NONE)
		{
			UE_LOG(LogTemp, Error, TEXT("joint: %sAxis XYZ value error: %s"), *NodeName, *AxisStr);
			continue;
		}

		if (Index == 0)
		{
			JointRotationAxes.Add(FName(NodeName), EAxis::X);
		}
		else if (Index == 1)
		{
			JointRotationAxes.Add(FName(NodeName), EAxis::Y);
		}
		else if (Index == 2)
		{
			JointRotationAxes.Add(FName(NodeName), EAxis::Z);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("joint: %sAxis XYZ value error: %s"), *NodeName, *AxisStr);
			return false;
		}
	}

	return true;
}

void UC_ROSDriverComponent::OnImageMsgReceived(const UC_BaseROSMsg* ROSMsg)
{
	const UC_ImageMsg* ImageMsg = Cast<UC_ImageMsg>(ROSMsg);
	if (ImageMsg == nullptr)
		return;

	 // 查找是哪个相机的消息
	FString ImageItemName;
	for (const auto& Pair: ImageTopicConfigurationItemValues)
	{
		if (ImageMsg->GetTopicName() == Pair.Value)
		{
			ImageItemName = Pair.Key;
			break;
		}
	}
	if (ImageItemName.IsEmpty())
		return;

	EROSImageEncoding ROSImageEncoding = ImageMsg->GetEncoding();
	if (ROSImageEncoding != EROSImageEncoding::rgb8 && ROSImageEncoding != EROSImageEncoding::mono16 && ROSImageEncoding != EROSImageEncoding::mono8)
		return;
	
	if (!ImageTextures.Contains(ImageItemName))
	{
		EPixelFormat PixelFormat;
		if (ROSImageEncoding == EROSImageEncoding::rgb8 || ROSImageEncoding == EROSImageEncoding::mono8 )
			PixelFormat = PF_R8G8B8A8;
		else if (ROSImageEncoding == EROSImageEncoding::mono16)
		{
			PixelFormat = PF_R8G8B8A8;
		}
		else
		{
			return;
		}
		UTexture2D* Texture2D = UTexture2D::CreateTransient(ImageMsg->GetWidth(), ImageMsg->GetHeight(), PixelFormat);
		OnImageItemChanged.Broadcast(ImageItemName);
		
		ImageTextures.Add(ImageItemName, Texture2D);
	}
	else
	{
		// 检查新的图像数据是否发送了变化
		EPixelFormat PixelFormat;
		if (ROSImageEncoding == EROSImageEncoding::rgb8 || ROSImageEncoding == EROSImageEncoding::mono8 )
			PixelFormat = PF_R8G8B8A8;
		else if (ROSImageEncoding == EROSImageEncoding::mono16)
		{
			PixelFormat = PF_R8G8B8A8;
		}
		else
		{
			return;
		}
		
		UTexture2D* Texture = ImageTextures[ImageItemName];
		if (ImageMsg->GetWidth() != Texture->GetSizeX() || ImageMsg->GetHeight() != Texture->GetSizeY() || PixelFormat != Texture->GetPixelFormat())
		{
			ImageTextures[ImageItemName] = UTexture2D::CreateTransient(ImageMsg->GetWidth(), ImageMsg->GetHeight(), PixelFormat);
			OnImageItemChanged.Broadcast(ImageItemName);
		}
	}

	UTexture2D* Texture = ImageTextures[ImageItemName];
	void* TextureData = ImageTextures[ImageItemName]->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
	const uint8* ImageData = ImageMsg->GetRawImageData();
	uint32 Width = ImageMsg->GetWidth();
	uint32 Height = ImageMsg->GetHeight();
	// 复制数据到纹理
	if (ROSImageEncoding == EROSImageEncoding::rgb8)
	{
		
		// 将 rgb8 转换为 RGBA
		for (uint32 Y = 0; Y < Height; ++Y)
		{
			for (uint32 X = 0; X < Width; ++X)
			{
				int32 Index = (Y * ImageMsg->GetWidth() + X) * 3; // RGB
				uint8* DestPixel = static_cast<uint8*>(TextureData) + (Y * ImageMsg->GetWidth() + X) * 4; // RGBA
				DestPixel[0] = ImageData[Index];     // R
				DestPixel[1] = ImageData[Index + 1]; // G
				DestPixel[2] = ImageData[Index + 2]; // B
				DestPixel[3] = 255;                   // A
			}
		}
		
	}
	else if (ROSImageEncoding == EROSImageEncoding::mono16)
	{
		const uint16* Mono16ImageData = reinterpret_cast<const uint16*>(ImageData);
		// 将 mono16 转换为 RGBA
		for (uint32 Y = 0; Y < Height; ++Y)
		{
			for (uint32 X = 0; X < Width; ++X)
			{
				// 映射到0~255
				uint8 GrayValue = Mono16ImageData[Y * Width + X] / 255;
				uint8* DestPixel = static_cast<uint8*>(TextureData) + (Y * ImageMsg->GetWidth() + X) * 4; // RGBA
				DestPixel[0] = GrayValue;		// R
				DestPixel[1] = GrayValue;		// G
				DestPixel[2] = GrayValue;		// B
				DestPixel[3] = 255;             // A
			}
		}
	}
	else
	{
		// 将 mono8 转换为 RGBA
		for (uint32 Y = 0; Y < Height; ++Y)
		{
			for (uint32 X = 0; X < Width; ++X)
			{
				uint8 GrayValue = ImageData[Y * Width + X];
				uint8* DestPixel = static_cast<uint8*>(TextureData) + (Y * ImageMsg->GetWidth() + X) * 4; // RGBA
				DestPixel[0] = GrayValue;		// R
				DestPixel[1] = GrayValue;		// G
				DestPixel[2] = GrayValue;		// B
				DestPixel[3] = 255;             // A
			}
		}
	}

	Texture->GetPlatformData()->Mips[0].BulkData.Unlock();
	Texture->UpdateResource();
}

void UC_ROSDriverComponent::OnCompressImageMsgReceived(const UC_BaseROSMsg* RosMsg)
{
	const UC_CompressedImageMsg* ImageMsg = Cast<UC_CompressedImageMsg>(RosMsg);
	if (ImageMsg == nullptr || ImageMsg->GetRawImageData() == nullptr || ImageMsg->GetLength() == 0)
		return;

	 // 查找是哪个相机的消息
	FString ImageItemName;
	for (const auto& Pair: ImageTopicConfigurationItemValues)
	{
		if (ImageMsg->GetTopicName() == Pair.Value)
		{
			ImageItemName = Pair.Key;
			break;
		}
	}
	if (ImageItemName.IsEmpty())
		return;

	// 将压缩数据转换为OpenCV Mat
	std::vector<uchar> ImageBuffer(ImageMsg->GetRawImageData(), ImageMsg->GetRawImageData() + ImageMsg->GetLength());
	cv::Mat DecodedImage = imdecode(ImageBuffer, cv::IMREAD_COLOR);
	if (DecodedImage.empty())
	{
		return;
	}

	// 添加alpha通道
	cv::Mat RGBAImage;
	cv::cvtColor(DecodedImage, RGBAImage, cv::COLOR_BGR2BGRA);
	
	// 设置alpha通道为255(完全不透明)
	std::vector<cv::Mat> channels;
	cv::split(RGBAImage, channels);
	channels[3] = cv::Mat(RGBAImage.rows, RGBAImage.cols, CV_8UC1, cv::Scalar(255));
	cv::merge(channels, RGBAImage);

	// 将OpenCV的RGBAImage.data转换为TArray<uint8>
	TArray<uint8> ImageData;
	const int32 DataSize = RGBAImage.cols * RGBAImage.rows * 4; // 4通道RGBA
	ImageData.SetNumUninitialized(DataSize);
	FMemory::Memcpy(ImageData.GetData(), RGBAImage.data, DataSize);

	// 使用转换后的TArray<uint8>写入HDF5
	UC_HDF5Util::WriteImageToHDF5(ImageItemName, ImageData, RGBAImage.cols, RGBAImage.rows, 4);
	if (!ImageTextures.Contains(ImageItemName))
	{
		UTexture2D* Texture2D = nullptr;
		Texture2D = FOpenCVHelper::TextureFromCvMat(RGBAImage, Texture2D);
		if (Texture2D == nullptr)
			return;
	
		ImageTextures.Add(ImageItemName, Texture2D);
		OnImageItemChanged.Broadcast(ImageItemName);
	}
	else
	{
		UTexture2D* OldTexture2D = ImageTextures[ImageItemName];
		UTexture2D* Texture2D = FOpenCVHelper::TextureFromCvMat(RGBAImage, OldTexture2D);
		if (Texture2D == nullptr)
			return;
	
		if (Texture2D != OldTexture2D)
		{
			ImageTextures[ImageItemName] = Texture2D;
			OnImageItemChanged.Broadcast(ImageItemName);
		}
	}
}

void UC_ROSDriverComponent::OnIMUMsgReceived(const UC_BaseROSMsg* RosMsg)
{
	const UC_ImuMsg* IMUMsg = Cast<UC_ImuMsg>(RosMsg);
	if (IMUMsg == nullptr) return;

	Orientation =IMUMsg->GetOrientation();

	AngularVelocity =IMUMsg->GetAngularVelocity();

	LinearAcceleration =IMUMsg->GetLinearAcceleration();
	UC_HDF5Util::WriteIMUDataToHDF5(TEXT("IMU"), Orientation, AngularVelocity, LinearAcceleration);
}

