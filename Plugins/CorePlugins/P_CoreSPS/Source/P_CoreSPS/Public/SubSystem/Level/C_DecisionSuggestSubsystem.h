// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利

#pragma once

#include "CoreMinimal.h"
#include "LevelInstance/LevelInstanceSubsystem.h"
#include "C_DecisionSuggestSubsystem.generated.h"

/**
 * 
 */

//决策建议表结构体
USTRUCT(BlueprintType)
struct FStructDecision
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(BlueprintReadWrite)
	FString level_nameOfMap;

	UPROPERTY(BlueprintReadWrite)
	FString nameOfScenario_main;

	UPROPERTY(BlueprintReadWrite)
	FString start_timeOfSim_main;

	UPROPERTY(BlueprintReadWrite)
	FString nameOfSim_main;

	UPROPERTY(BlueprintReadWrite)
	FString AssessSuggest;

	//UPROPERTY(BlueprintReadWrite)
	int32 CountType0 = 0;

	UPROPERTY(BlueprintReadWrite)
	FString CountType0Percent;

	//UPROPERTY(BlueprintReadWrite)
	int32 CountType1 = 0;

	UPROPERTY(BlueprintReadWrite)
	FString CountType1Percent;

	int32 CountType2 = 0;

	UPROPERTY(BlueprintReadWrite)
	FString CountType2Percent;

	UPROPERTY(BlueprintReadWrite)
	int64 simid = 0;

	UPROPERTY(BlueprintReadWrite)
	int32 Type = 0;

};

//场景管理表结构体
USTRUCT(BlueprintType)
struct FStructSceneManage
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(BlueprintReadWrite)
	FString Name;

	UPROPERTY(BlueprintReadWrite)
	FString Time;

	UPROPERTY(BlueprintReadWrite)
	FString LevelName;

	UPROPERTY(BlueprintReadWrite)
	int64 MapId = 0;

	UPROPERTY(BlueprintReadWrite)
	int32 Type = 0;

	UPROPERTY(SaveGame, EditAnywhere, BlueprintReadWrite)
	TArray<uint8> picture;

};

//训练管理表结构体(想定）
USTRUCT(BlueprintType)
struct FStructTrainManage
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(BlueprintReadWrite)
	FString Name;

	UPROPERTY(BlueprintReadWrite)
	int64 MapId = 0;

	UPROPERTY(BlueprintReadWrite)
	int64 ScenarioId = 0;

	UPROPERTY(BlueprintReadWrite)
	FStructSceneManage SceneData;

	UPROPERTY(BlueprintReadWrite)
	FString Time;

	UPROPERTY(BlueprintReadWrite)
	int32 Type = 0;
	
	UPROPERTY(BlueprintReadWrite)
    FString Author;

	UPROPERTY(BlueprintReadWrite)
	FString Introduce;

	UPROPERTY(SaveGame, EditAnywhere, BlueprintReadWrite)
	TArray<uint8> picture;

};

//导调控制表结构体
USTRUCT(BlueprintType)
struct FStructSimControl
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(BlueprintReadWrite)
	int64 SimId = 0;

	UPROPERTY(BlueprintReadWrite)
	FString Name;

	UPROPERTY(BlueprintReadWrite)
	int64 ScenarioId = 0;
	UPROPERTY(BlueprintReadWrite)
	int64 MapId = 0;

	UPROPERTY(BlueprintReadWrite)
	FStructTrainManage TrainData;

	UPROPERTY(BlueprintReadWrite)
	FString Time;

};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FSaveSceneComplete,bool,bIsSuccess,const FString&,Tips,bool,Yes,bool,No);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FSaveTrainComplete,bool,bIsSuccess,const FString&,Tips,bool,Yes,bool,No);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FDeleteEntity,const FString&,Tips,bool,Yes,bool,No);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FImportFBX,bool,bIsComplete);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FLoadComplete,bool,bIsComplete);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FImportStarted,FName,FileName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FImportProgress,float,Progress);


UCLASS()
class P_CORESPS_API UC_DecisionSuggestSubsystem : public ULevelInstanceSubsystem
{
	GENERATED_BODY()

public:
	static UC_DecisionSuggestSubsystem* Get();
	virtual bool ShouldCreateSubsystem(UObject* Outer) const { return true; };
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;
	
public:
	
	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FSaveSceneComplete SaveSceneComplete;
	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FSaveTrainComplete SaveTrainComplete;
	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FDeleteEntity DeleteEntity;
	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FImportFBX ImportFBX;
	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FLoadComplete LoadComplete;

	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FImportStarted OnImportStarted;
	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FImportProgress OnImportProgressChanged;


	UFUNCTION(BlueprintCallable)
	bool CheckStringIsValid(const FString& str,const FString Reg);

	UFUNCTION(BlueprintCallable)
	void OutSimulationSterLog();

public:

	//将图片数据存入数据库
	bool UpdateSnapshootSPS(const FString& TableName,const int64& id, const TArray<uint8>& Data);
	//获取图片数据
	bool GetSnapshootSPS(const FString& TableName,const int64& id, TArray<uint8>& Data);

	//插入数据
	bool UpdateOriginSql(const int64& id, const float& X,const float& Y,const float& Z);

	bool GetOriginByDB(const int64& id, float& X,float& Y,float& Z);

	UFUNCTION(BlueprintCallable,Category="Origin")
	bool GetOriginByMemory(FVector& OutOrigin);
	UFUNCTION(BlueprintCallable,Category="Origin")
	void SetOrigin(FVector InOrigin);

	FVector MemoryOrigin;
	bool bMemoryOriginIsNull=true;
	

	
	/*UPROPERTY(config ,Category="CustomIni")
	bool bServer=false;
	
	FString ConfigPath = FPaths::ProjectDir() / TEXT("Config/User_OriginSetting.ini");

	void LoadCustomConfig()
	{
		GConfig->Flush(true, ConfigPath);
		ReloadConfig(this->GetClass(), *ConfigPath);
	}

	void SaveCustomConfig()
	{
		SaveConfig(CPF_Config, *ConfigPath);
		GConfig->Flush(false, ConfigPath);
	}*/


#pragma region Decision

	//查询决策建议表所需数据
	TArray<FStructDecision> QueryDSData();

	/*-----------------------决策--------------------------*/
	//通过筛选项查询数据
	TArray<FStructDecision> QueryDSDataByFilter(const FString& TrainName,const FString& TrainType,const FString& AssessSuggest,const FString& StartTime, const FString& EndTime);

	//查询
	TArray<FStructDecision> QuerySql(const FString& Sql);


	//获取训练类别对应的地图路径
	FString GetTrainPath(const FString& TrainType);
#pragma endregion


#pragma region SceneManage
	/*-----------------------场景管理--------------------------*/
	//查询场景管理所需数据
	TArray<FStructSceneManage> QuerySceneMapData();

	FStructSceneManage GetSceneDataById(const int64& id);

		
	//通过筛选项查询场景管理数据
	TArray<FStructSceneManage> QuerySceneDataByFilter(const FString& SceneName, const FString& SceneType, const FString& StartTime, const FString& EndTime);

	//查询场景地图
	TArray<FStructSceneManage> QuerySceneMap(const FString& Sql);

	//检查是否有重名的地图名称
	bool CheckMapNameisExits(const FString& mapName);

	//插入数据
	bool InsertMapSql(const int64& id, const FString& mapName, const FString& createTime, const FString& mapLevelName, const FString& modifyTime, const int32& built_in, const int32& type);
	
	//更新场景地图名称
	bool UpdateSceneMapData(const int64 Id, const FString& NewName, const FString& ModifyTime);

	bool DeleteSceneMapData(const int64& id);

	FStructSceneManage GetSceneEntityDataById(const int64& id);

#pragma endregion


#pragma region TrainManage
	/*-----------------------训练管理--------------------------*/
	//查询训练管理所需数据
	TArray<FStructTrainManage> QueryTrainMapData();

	//通过筛选项查询训练管理数据
	TArray<FStructTrainManage> QueryTrainDataByFilter(const FString& SceneName, const FString& SceneType, const FString& StartTime, const FString& EndTime);

	//根据sql查询训练管理数据
	TArray<FStructTrainManage> QueryTrainMap(const FString& Sql);

	//检查是否有重名的训练名称
	bool CheckTrainNameisExits(const FString& mapName);

	//插入数据
	bool InsertTrainMapSql(const int64& id, const FString& TrainName, const int64& MapId, const FString& createTime, const int32& type,const FString& Author,const FString& Introduce);

	//更新场景地图名称
	bool UpdateTrainMapData(const int64 Id, const FString& NewName, const FString& ModifyTime,const FString& Author, const FString& Introduce);

	bool DeleteTrainMapData(const int64& id);

	FStructTrainManage GetTrainDataById(const int64& id);

	bool UpdateTrainToDB(const FStructTrainManage& Tarin);
	
#pragma endregion


#pragma region SimControl

	/*-----------------------导调控制--------------------------*/
	//查询导调控制所需数据
	TArray<FStructSimControl> QuerySimMapData();

	//通过筛选项查询导调控制数据
	TArray<FStructSimControl> QuerySimDataByFilter(const FString& SceneName, const FString& SceneType, const FString& StartTime, const FString& EndTime);


	

	
private:
	//根据sql查询导调控制数据
	TArray<FStructSimControl> QuerySimMap(const FString& Sql);
#pragma endregion

public:

#pragma region UnifiedModify
	bool JudgeIsPatrolByMapPath(const FString& Path);
	bool JudgeIsPatrolByMapName(const FString& Path);
	TArray<FString> GetFilterPath();
	TArray<FString> GetMapName();

	FString Partol = TEXT("0");
	FString Target = TEXT("1");
	FString NoType = TEXT("-1");


#pragma endregion
};




