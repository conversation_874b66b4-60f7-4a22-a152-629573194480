// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利

#pragma once

#include "CoreMinimal.h"
#include "DataStruct/C_ShootingRecord.h"
#include "Subsystems/WorldSubsystem.h"
#include "C_TargetScoringSubSystem.generated.h"

class AC_HumanoidRobot;
struct FShootingRecord;
class UC_BaseWeaponComponent;
class UC_UdpReceiver;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FNewShootingRecordCreated, const FShootingRecord&, ShootingRecord);

/**
 * 打靶计分子系统
 */
UCLASS()
class P_CORESPS_API UC_TargetScoringSubSystem : public UWorldSubsystem
{
	GENERATED_BODY()

public:
	static UC_TargetScoringSubSystem* Get();
	virtual bool ShouldCreateSubsystem(UObject* Outer) const override;
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	void NotifyFireResult(const UC_BaseWeaponComponent* Weapon, const FHitResult& HitResult);

	UPROPERTY(BlueprintAssignable)
	FNewShootingRecordCreated OnNewShootingRecordCreated;
private:
	bool IsTargetPracticeMode() const;
	
	/**
	 * 接收到外部打靶计分系统数据时
	 * @param RawData 
	 * @param IP 
	 * @param Port 
	 */
	UFUNCTION()
	void OnDataReceived(const TArray<uint8>& RawData, const FString& IP, const int& Port);

	/**
	 * 把接收到的数据转换成打靶记录结构体
	 * @param JsonObj 
	 * @param ShootingRecord 
	 * @return 
	 */
	bool ConvertToShootingRecord(const TSharedPtr<FJsonObject>& JsonObj, FShootingRecord& ShootingRecord);

	/**
	 * 根据打靶记录创建数据库记录
	 * @param ShootingRecord 
	 */
	void CreateShootingRecord(const FShootingRecord& ShootingRecord) const;

	/**
	 * 根据枪编号，查找枪的拥有者
	 * @param WeaponID 
	 * @return 
	 */
	AC_HumanoidRobot* FindWeaponOwner(int WeaponID) const;

	void InitRoundID();
	
	/**
	 * 监控模式下，接受打靶计分系统的接收器
	 */
	UPROPERTY()
	UC_UdpReceiver* ExternalSystemReceiver;

	/**
	 * 枪开火序号
	 */
	UPROPERTY()
	TMap<int, int> WeaponFireSequences;

	UPROPERTY()
	int CurrentRoundID;
};
