// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利

#pragma once

#include "CoreMinimal.h"
#include "BaseEntity/Person/C_Combatant.h"
#include "Interface/C_ConfigurableObjectContainerInterface.h"
#include "Interface/C_ConfigurableObjectInterface.h"
#include "C_HumanoidRobot.generated.h"

UCLASS()
class P_CORESPS_API AC_HumanoidRobot : public AC_Combatant, public IC_ConfigurableObjectContainerInterface, public IC_ConfigurableObjectInterface
{
	GENERATED_BODY()

public:
	AC_HumanoidRobot(const FObjectInitializer& ObjectInitializer);
	void OnRecordJointState();
	virtual void BeginPlay() override;
	
	UFUNCTION(BlueprintSetter)
	void SetRobotID(int InValue);

	UFUNCTION(BlueprintGetter, BlueprintPure)
	int GetRobotID() const
	{
		return RobotID;
	}

	UFUNCTION(BlueprintImplementableEvent)
	USkeletalMeshComponent* GetAnimationMesh();
	
	bool ContainWeapon(int WeaponID);
	virtual float PlayAnimMontage(class UAnimMontage* AnimMontage, float InPlayRate = 1.f, FName StartSectionName = NAME_None) override;	
protected:
	/**
	 * 设备ID，用于标识设备
	 */
	UPROPERTY(BlueprintReadWrite, BlueprintSetter=SetRobotID, BlueprintGetter=GetRobotID, SaveGame)
	int RobotID;
	FTimerHandle TimerHandle;

#pragma region EntityConfigurationInterface

public:
	virtual TArray<TScriptInterface<IC_ConfigurableObjectInterface>> GetAllConfigurableComponent_Implementation() override;

	virtual FString GetConfigurableObjectName_Implementation() const override;
	virtual TArray<FConfigurationItemInfo> GetConfigurationItemInfos_Implementation() const override;
	virtual bool SetIntItem_Implementation(const FString& ItemName, const int& Value) override;
	virtual bool SetFloatItem_Implementation(const FString& ItemName, const float& Value) override;
	virtual bool SetStringItem_Implementation(const FString& ItemName, const FString& Value) override;
	
#pragma endregion 
};
