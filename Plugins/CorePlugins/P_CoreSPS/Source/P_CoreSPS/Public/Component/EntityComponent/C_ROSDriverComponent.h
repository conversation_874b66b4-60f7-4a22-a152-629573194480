// 版权所有：©2024 重庆平戎科技有限公司 保留所有权利

#pragma once

#include "CoreMinimal.h"
#include "EMSCompSaveInterface.h"
#include "Components/ActorComponent.h"
#include "Containers/CircularQueue.h"
#include "DataStruct/C_JointState.h"
#include "Interface/C_ConfigurableObjectInterface.h"
#include "C_ROSDriverComponent.generated.h"


class UC_GameInstanceSPS;
class UC_BaseROSMsg;
class UC_Topic;
class AC_GameMode;
class AC_BaseCharacter;
class FROSBaseMsg;
class UTopic;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FImageItemChanged, const FString&, ImageName);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class P_CORESPS_API UC_ROSDriverComponent : public UActorComponent, public IC_ConfigurableObjectInterface, public IEMSCompSaveInterface
{
	GENERATED_BODY()

public:
	UC_ROSDriverComponent();

	UFUNCTION(BlueprintPure)
	bool GetSkeletonState(FSkeletonState& SkeletonState);

	/**
	 * 获取图像的名称
	 * @return 
	 */
	UFUNCTION(BlueprintPure)
	TArray<FString> GetImageItemNames()const;

	/**
	 * 获取关节的修改数据
	 * @param JointName 
	 * @return 
	 */
	UFUNCTION(BlueprintPure)
	const TMap<FName, bool>& GetJointStateModifier() const;
	
	/**
	 * 获取图像对应的Texture
	 * @param ItemName 
	 * @return 
	 */
	UFUNCTION(BlueprintPure)
	UTexture2D* GetImageTexture(const FString& ItemName) const;
	
	/**
	 * 获取IMU数据
	 * @param OrientationLoc 
	 * @param AngularVelocityLoc 
	 * @param LinearAccelerationLoc 
	 */
	UFUNCTION(BlueprintPure)
    void GetIMUData(FQuat& OrientationLoc,FVector& AngularVelocityLoc,FVector& LinearAccelerationLoc);

	UPROPERTY(BlueprintAssignable)
	FImageItemChanged OnImageItemChanged;

#pragma region ConfigurableObjectInterface
	virtual FString GetConfigurableObjectName_Implementation() const override;
	virtual TArray<FConfigurationItemInfo> GetConfigurationItemInfos_Implementation() const override;
	virtual bool SetFloatItem_Implementation(const FString& ItemName, const float& Value) override;
	virtual bool SetStringItem_Implementation(const FString& ItemName, const FString& Value) override;
	virtual bool SetIntItem_Implementation(const FString& ItemName, const int& Value) override;

#pragma endregion

#pragma region EMSCompSaveInterface
	virtual void ComponentLoaded_Implementation() override;
	virtual void ComponentPreSave_Implementation() override;
#pragma endregion

protected:
	UFUNCTION()
	
	virtual void BeginPlay() override;

private:
	UFUNCTION()
	void OnOdometryMsgReceived(const UC_BaseROSMsg* ROSMsg);
	
	UFUNCTION()
	void OnJointStatesMsgReceived(const UC_BaseROSMsg* ROSMsg);
	bool ParseURDF();

	UFUNCTION()
	void OnImageMsgReceived(const UC_BaseROSMsg* RosMsg);

	UFUNCTION()
	void OnCompressImageMsgReceived(const UC_BaseROSMsg* RosMsg);
	
	UFUNCTION()
    void OnIMUMsgReceived(const UC_BaseROSMsg* RosMsg);
	
protected:
	/**
	 * 里程计话题名称
	 */
	UPROPERTY(BlueprintReadOnly, EditAnywhere, SaveGame, Category="ROS")
	FString OdometryTopicName;

	/**
	 * 关节状态话题名称
	 */
	UPROPERTY(BlueprintReadOnly, EditAnywhere, SaveGame, Category="ROS")
	FString JointStatesTopicName;

	static TArray<FString> ImageConfigurationNames;

	/**
	 * 图像主题配置项值
	 *
	 * 键位配置项名称，值位配置项的值（主题名称）
	 */
	UPROPERTY(BlueprintReadOnly, EditAnywhere, SaveGame, Category="ROS")
	TMap<FString, FString> ImageTopicConfigurationItemValues;
	
	/**
    * IMU主题配置项值
    *
    * 键位配置项名称，值位配置项的值（主题名称）
    */
    UPROPERTY(BlueprintReadOnly, EditAnywhere, SaveGame, Category="ROS")
    TMap<FString, FString> IMUTopicConfigurationItemValues;

	
private:
	/**
	 * 历程计主题
	 */
	UPROPERTY()
	TObjectPtr<UC_Topic> OdometryTopic;

	/**
	 * 关节状态主题
	 */
	UPROPERTY()
	TObjectPtr<UC_Topic> JointStatesTopic;
	
	/**
	 * 图像主题
	 *
	 * 键为配置项的名称，值为对应的Topic
	 */
	UPROPERTY()
	TMap<FString, TObjectPtr<UC_Topic>> ImageTopics;

	/**
	 * 图像的纹理
	 *
	 * 键为配置项的名称，值为对应的该相机获取到的纹理（通过ros获取到原始图像数据，然后更新纹理）
	 */
	UPROPERTY()
	TMap<FString, TObjectPtr<UTexture2D>> ImageTextures;

	/**
	 * 当前骨骼网格体所有的关节名称
	 */
	UPROPERTY()
	TArray<FName> SkeletonJointNames;
	
	/**
     * IMU主题
     *
     * 键为配置项的名称，值为对应的Topic
     */
    UPROPERTY()
    TMap<FString, TObjectPtr<UC_Topic>> IMUTopics;
    
    UPROPERTY()
    FQuat Orientation;
    UPROPERTY()
    FVector AngularVelocity;
    UPROPERTY()
    FVector LinearAcceleration;

	/**
	 * 网格体状态队列（缓存部分数据，避免数据竞争）
	 */
	TCircularQueue<FSkeletonState> SkeletonStateQueue = TCircularQueue<FSkeletonState>(120);

	/**
	 * 关节旋转轴
	 */
	TMap<FName, EAxis::Type> JointRotationAxes;
	
	/**
	 * GameMode指针
	 */
	UPROPERTY()
	TWeakObjectPtr<AC_GameMode> TESPGameMode;

	/**
	 * 拥有者的指针
	 */
	UPROPERTY()
	TWeakObjectPtr<AC_BaseCharacter> Owner;

	UPROPERTY()
	TWeakObjectPtr<UC_GameInstanceSPS> SPSGameInstance;

	bool bHasReceivedOdometryMsg = false;
	FVector FirstOdometryPosition;
	FRotator FirstOdometryRotator;
	FRotator FirstInverseOdometryRotator;


	bool bRobotOriginPositionInitialized = false;
	FVector RobotOriginPosition;
	FRotator RobotOriginRotator;

	/**
	 * 关节是否取负数
	 */
	TMap<FName, bool> JointStateNegative;
};
