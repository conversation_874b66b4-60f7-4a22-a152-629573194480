
// Copyright <PERSON><PERSON><PERSON> Gurtovoy 2000-2004
// Copyright <PERSON> 2003-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/set/set30.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20
    >
struct set21
    : s_item<
          T20
        , typename set20< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19 >::item_
        >
{
    typedef set21 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21
    >
struct set22
    : s_item<
          T21
        , typename set21< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20 >::item_
        >
{
    typedef set22 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22
    >
struct set23
    : s_item<
          T22
        , typename set22< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21 >::item_
        >
{
    typedef set23 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23
    >
struct set24
    : s_item<
          T23
        , typename set23< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22 >::item_
        >
{
    typedef set24 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    >
struct set25
    : s_item<
          T24
        , typename set24< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23 >::item_
        >
{
    typedef set25 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25
    >
struct set26
    : s_item<
          T25
        , typename set25< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23
        , T24 >::item_
        >
{
    typedef set26 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26
    >
struct set27
    : s_item<
          T26
        , typename set26< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24
        , T25 >::item_
        >
{
    typedef set27 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27
    >
struct set28
    : s_item<
          T27
        , typename set27< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26 >::item_
        >
{
    typedef set28 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28
    >
struct set29
    : s_item<
          T28
        , typename set28< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27 >::item_
        >
{
    typedef set29 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    >
struct set30
    : s_item<
          T29
        , typename set29< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28 >::item_
        >
{
    typedef set30 type;
};

}}
